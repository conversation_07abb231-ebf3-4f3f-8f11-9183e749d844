import { type PropsWithChildren, useEffect, useState } from "react";
import { FirebaseAuthTypes, getAuth } from "@react-native-firebase/auth";
import { UserContext } from "./user-context.ts";

/**
 * AuthProvider
 * This component is used to provide authentication to the app.
 * It will listen for auth changes and update the user.
 *
 * NOTE: make sure to configure the webClientId for Google Sign-In
 * GoogleSignin.configure({
 *   webClientId:
 *     "xxxxxxxxxx-xxxxxxxxxxxxxxxxxxxxxx.apps.googleusercontent.com",
 * });
 */

export function AuthProvider({ children }: PropsWithChildren) {
  const [loading, setLoading] = useState(true);
  const [initializing, setInitializing] = useState(true);
  const [user, setUser] = useState<FirebaseAuthTypes.User | null>(
    getAuth().currentUser,
  );

  useEffect(() => {
    if (user) {
      setLoading(false);
      setInitializing(false);
    }
    return getAuth().onAuthStateChanged((user) => {
      console.log("onAuthStateChanged", user);
      setUser(user);
      if (initializing) setInitializing(false);
      if (loading) setLoading(false);
    });
  }, []);

  if (initializing) return null;

  // TODO: throw suspense
  if (loading) return null;

  return <UserContext.Provider value={user}>{children}</UserContext.Provider>;
}
