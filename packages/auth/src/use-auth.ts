import auth, { getAuth } from "@react-native-firebase/auth";
import { GoogleSignin } from "@react-native-google-signin/google-signin";
import { TAG } from "./tag.ts";
import { useContext } from "react";
import { UserContext } from "./user-context.ts";

export function useAuth() {
  const user = useContext(UserContext);

  // Sign in with email and password
  async function loginWithEmailAndPassword(email: string, password: string) {
    console.debug("Logging in with email and password...");
    try {
      await getAuth().signInWithEmailAndPassword(email, password);
    } catch (error) {
      console.error("Login failed:", error);
      throw error;
    }
  }

  // Sign in with Google
  async function loginWithGoogle() {
    console.debug(TAG, "Logging in with Google...");
    try {
      await GoogleSignin.hasPlayServices({
        showPlayServicesUpdateDialog: true,
      });
      const res = await GoogleSignin.signIn();
      const idToken = res.data?.idToken;
      if (!idToken) throw new Error("Google sign-in failed: No idToken");

      const googleCredential = auth.GoogleAuthProvider.credential(idToken);
      await getAuth().signInWithCredential(googleCredential);
    } catch (error) {
      console.error(TAG, "Google sign-in failed:", error);
      throw error;
    }
  }

  // Forgot password
  async function resetPassword(email: string) {
    console.debug(TAG, "Sending password reset email...");
    try {
      await getAuth().sendPasswordResetEmail(email);
      console.debug(TAG, `Password reset email sent to ${email}`);
    } catch (error) {
      console.error(TAG, "Password reset failed:", error);
      throw error;
    }
  }

  // Logout function
  async function logout() {
    console.debug(TAG, "Logging Out...");
    await getAuth().signOut();
  }

  // TODO: re-check
  // Check if both email and phone are verified
  function isVerified() {
    const currentUser = getAuth().currentUser;
    if (currentUser?.emailVerified && currentUser?.phoneNumber) {
      // Navigate to authenticated area
      // router.push("/(authenticated)/(driver)");
      return true;
    } else if (!currentUser?.emailVerified) {
      // Navigate to email verification screen
      // todo : Need a screen which show to  verify the email and  resend the verification email.
      // router.push("/(unauthenticated)/VerifyMobileScreen");
      return false;
      // } else if (!currentUser?.phoneNumber) {
      // Navigate to phone verification screen
      // router.push('/(unauthenticated)/verifyPhoneScreen');
    }
  }

  async function signupWithEmailAndPassword(email: string, password: string) {
    console.debug(TAG, "Signing up with email and password...");
    // try {
    //   await getAuth().createUserWithEmailAndPassword(email, password);
    // } catch (e: NativeFirebaseError) {
    //   if ("code" in e) throw e;
    //   switch (e.code) {
    //     case "auth/email-already-in-use":
    //       throw new Error("Email already in use");
    //     case "auth/invalid-email":
    //       throw new Error("Invalid email address");
    //     case "auth/operation-not-allowed":
    //       throw new Error("Email/password accounts are not enabled");
    //     case "auth/weak-password":
    //       throw new Error("Weak password");
    //     default:
    //       throw new Error("Unknown error occurred");
    //   }
    // }
    return await getAuth().createUserWithEmailAndPassword(email, password);
  }

  return {
    user,
    loginWithGoogle,
    resetPassword,
    loginWithEmailAndPassword,
    isVerified,
    signupWithEmailAndPassword,
    logout,
  };
}
