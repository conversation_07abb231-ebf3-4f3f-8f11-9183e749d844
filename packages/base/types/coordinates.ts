export interface Coordinates {
  latitude: number;
  longitude: number;
}

export function coordsToString(coordinated: Coordinates) {
  return `${coordinated.latitude},${coordinated.longitude}`;
}

export function parseCoords(coords: string): Coordinates | undefined {
  const [latitude, longitude] = coords.split(",");

  if (!latitude || !longitude) {
    return undefined;
  }

  return {
    latitude: parseFloat(latitude),
    longitude: parseFloat(longitude),
  };
}
