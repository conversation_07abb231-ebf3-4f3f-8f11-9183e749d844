import { useSuspenseQuery } from "@tanstack/react-query";
import * as Location from "expo-location";
import type { Coordinates } from "@x/rn-shared";

export function useCurrentLocation() {
  return useSuspenseQuery({
    queryKey: ["current-location"],
    queryFn: async () => {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== "granted") {
        throw new Error("Permission to access location was denied");
      }

      const locationResult = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      return {
        latitude: locationResult.coords.latitude,
        longitude: locationResult.coords.longitude,
      } satisfies Coordinates;
    },
  });
}
