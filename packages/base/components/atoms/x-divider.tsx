import { Text, useTheme } from "react-native-paper";
import React, { type ComponentProps } from "react";
import { View, type ViewProps } from "react-native";

interface XDividerProps extends ViewProps {
  textProps?: ComponentProps<typeof Text>;
  text?: string;
}

export function XDivider({ text, style, textProps }: XDividerProps) {
  const { colors } = useTheme();

  return (
    <View
      style={[
        {
          flexDirection: "row",
          alignItems: "center",
        },
        style,
      ]}
    >
      <View
        style={{
          backgroundColor: colors.outlineVariant,
          flex: 1,
          height: 1,
        }}
      />
      {text && (
        <Text
          {...textProps}
          style={[
            { color: colors.outline, marginHorizontal: 12 },
            textProps?.style,
          ]}
        >
          {text}
        </Text>
      )}
      <View
        style={{
          backgroundColor: colors.outlineVariant,
          flex: 1,
          height: 1,
        }}
      />
    </View>
  );
}
