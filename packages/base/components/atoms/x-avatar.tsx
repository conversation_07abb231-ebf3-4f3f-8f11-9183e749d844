import { Image, type ImageProps } from "react-native";
import { useTheme } from "react-native-paper";
import { useState } from "react";

interface AvatarProps extends ImageProps {
  uri?: string;
}

export function XAvatar({ uri, ...imageProps }: AvatarProps) {
  const { colors } = useTheme();
  const [showFallback, setShowFallback] = useState(!uri);

  function handleError() {
    console.warn("Error loading image...");
    setShowFallback(true);
  }

  return (
    <Image
      source={
        showFallback
          ? require("@x/rn-shared/assets/images/unknown-user.png")
          : { uri }
      }
      onError={handleError}
      {...imageProps}
      style={[
        {
          width: 40,
          height: 40,
          borderRadius: 100,
          borderWidth: 2,
          borderColor: colors.outlineVariant,
        },
        imageProps.style,
      ]}
    />
  );
}
