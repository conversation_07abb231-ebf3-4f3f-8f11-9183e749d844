import { ActivityIndicator, TouchableRipple } from "react-native-paper";
import React, { useState } from "react";
import { Image, StyleSheet } from "react-native";
import { useAuth } from "@x/auth";

interface GoogleLoginButtonProps {
  onError?: (error: string) => void;
}

export function GoogleLoginButton({ onError }: GoogleLoginButtonProps) {
  const [googleLoading, setGoogleLoading] = useState(false);
  const { loginWithGoogle } = useAuth();

  const GoogleIcon = require("@x/rn-shared/assets/images/google-icon.png");

  const handleGoogleSignIn = async () => {
    setGoogleLoading(true);
    try {
      await loginWithGoogle();
    } catch (e) {
      onError?.("Failed to sign in with Google. Please try again.");
    } finally {
      setGoogleLoading(false);
    }
  };

  return (
    <TouchableRipple
      borderless
      style={styles.socialButton}
      onPress={handleGoogleSignIn}
      disabled={googleLoading}
    >
      {googleLoading ? (
        <ActivityIndicator />
      ) : (
        <Image source={GoogleIcon} style={styles.socialIcon} />
      )}
    </TouchableRipple>
  );
}

const styles = StyleSheet.create({
  socialButton: {
    width: 50,
    height: 50,
    borderRadius: 100,
    justifyContent: "center",
    alignItems: "center",
  },
  socialIcon: {
    width: 40,
    height: 40,
  },
});
