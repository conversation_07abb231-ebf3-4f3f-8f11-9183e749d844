import { z } from "zod";
import { useAuth } from "@x/auth";
import React, { useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Alert, View } from "react-native";
import { Button, HelperText, TextInput } from "react-native-paper";

const signInFormSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
});

export function SignInForm() {
  const { loginWithEmailAndPassword, resetPassword } = useAuth();
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [resetLoading, setResetLoading] = useState(false);

  const { control, formState, handleSubmit, getValues, setError } = useForm({
    defaultValues: {},
    resolver: zodResolver(signInFormSchema),
  });

  async function onSubmit(data: z.infer<typeof signInFormSchema>) {
    try {
      await loginWithEmailAndPassword(data.email, data.password);
    } catch (e) {
      setError("password", {
        type: "server",
        message: "Invalid email or password. Please try again.",
      });
    }
  }

  const handleForgotPassword = async () => {
    const email = getValues("email");
    if (!email) {
      Alert.alert(
        "Password Reset",
        "Please enter your email address to reset your password.",
      );
      return;
    }

    setResetLoading(true);
    try {
      await resetPassword(email);
      Alert.alert(
        "Password Reset",
        "A password reset link has been sent to your email.",
      );
    } catch (e) {
      Alert.alert(
        "Password Reset",
        "Failed to send password reset email. Please try again.",
      );
    } finally {
      setResetLoading(false);
    }
  };

  return (
    <View
      style={{
        width: "100%",
        gap: 8,
      }}
    >
      <Controller
        render={({ field }) => (
          <View>
            <TextInput
              label="Email"
              value={field.value}
              mode="outlined"
              textContentType="emailAddress"
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              onChangeText={field.onChange}
              onBlur={field.onBlur}
              error={!!formState.errors.email?.message}
            />
            {!!formState.errors.email?.message && (
              <HelperText
                type="error"
                visible={!!formState.errors.email?.message}
              >
                {formState.errors.email?.message}
              </HelperText>
            )}
          </View>
        )}
        name="email"
        control={control}
      />

      <Controller
        render={({ field }) => (
          <View>
            <TextInput
              label="Password"
              value={field.value}
              mode="outlined"
              textContentType="newPassword"
              secureTextEntry={!passwordVisible}
              right={
                <TextInput.Icon
                  onPress={() => setPasswordVisible(!passwordVisible)}
                  icon={passwordVisible ? "eye-off" : "eye"}
                />
              }
              autoCapitalize="none"
              autoCorrect={false}
              onChangeText={field.onChange}
              onBlur={field.onBlur}
              error={!!formState.errors.password?.message}
            />
            {!!formState.errors.password?.message && (
              <HelperText
                type="error"
                visible={!!formState.errors.password?.message}
              >
                {formState.errors.password?.message}
              </HelperText>
            )}
          </View>
        )}
        name="password"
        control={control}
      />

      {/* Forgot Password */}
      <Button
        compact
        mode="text"
        style={{ alignSelf: "flex-end" }}
        onPress={handleForgotPassword}
        loading={resetLoading}
      >
        Forgot Password?
      </Button>

      <Button
        mode="contained"
        style={{ marginTop: 16 }}
        onPress={handleSubmit(onSubmit)}
        loading={formState.isSubmitting}
      >
        Sign In
      </Button>
    </View>
  );
}
