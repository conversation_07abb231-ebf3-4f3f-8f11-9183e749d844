import { Controller, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button, HelperText, TextInput } from "react-native-paper";
import { View } from "react-native";
import { useAuth } from "@x/auth";
import React, { useState } from "react";

const signupFormSchema = z
  .object({
    email: z.string().email(),
    password: z.string().min(8),
    confirmPassword: z.string(),
  })
  .superRefine((data, ctx) => {
    if (data.password !== data.confirmPassword) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Passwords do not match",
        path: ["confirmPassword"],
      });
    }
  });

export function SignUpForm() {
  const { signupWithEmailAndPassword } = useAuth();
  const [passwordVisible, setPasswordVisible] = useState(false);

  const { control, formState, handleSubmit } = useForm({
    defaultValues: {},
    resolver: zod<PERSON><PERSON><PERSON>ver(signupFormSchema),
  });

  async function onSubmit(data: z.infer<typeof signupFormSchema>) {
    await signupWithEmailAndPassword(data.email, data.password);
  }

  return (
    <View
      style={{
        width: "100%",
        gap: 8,
      }}
    >
      <Controller
        render={({ field }) => (
          <View>
            <TextInput
              label="Email"
              value={field.value}
              mode="outlined"
              textContentType="emailAddress"
              keyboardType="email-address"
              autoCapitalize="none"
              autoCorrect={false}
              onChangeText={field.onChange}
              onBlur={field.onBlur}
              error={!!formState.errors.email?.message}
            />
            {!!formState.errors.email?.message && (
              <HelperText
                type="error"
                visible={!!formState.errors.email?.message}
              >
                {formState.errors.email?.message}
              </HelperText>
            )}
          </View>
        )}
        name="email"
        control={control}
      />

      <Controller
        render={({ field }) => (
          <View>
            <TextInput
              label="Password"
              value={field.value}
              mode="outlined"
              textContentType="newPassword"
              secureTextEntry={!passwordVisible}
              right={
                <TextInput.Icon
                  onPress={() => setPasswordVisible(!passwordVisible)}
                  icon={passwordVisible ? "eye-off" : "eye"}
                />
              }
              autoCapitalize="none"
              autoCorrect={false}
              onChangeText={field.onChange}
              onBlur={field.onBlur}
              error={!!formState.errors.password?.message}
            />
            {!!formState.errors.password?.message && (
              <HelperText
                type="error"
                visible={!!formState.errors.password?.message}
              >
                {formState.errors.password?.message}
              </HelperText>
            )}
          </View>
        )}
        name="password"
        control={control}
      />

      <Controller
        render={({ field }) => (
          <View>
            <TextInput
              label="Confirm Password"
              value={field.value}
              mode="outlined"
              autoCapitalize="none"
              secureTextEntry={!passwordVisible}
              autoCorrect={false}
              textContentType="newPassword"
              onChangeText={field.onChange}
              onBlur={field.onBlur}
              error={!!formState.errors.confirmPassword?.message}
            />
            {!!formState.errors.confirmPassword?.message && (
              <HelperText
                type="error"
                visible={!!formState.errors.confirmPassword?.message}
              >
                {formState.errors.confirmPassword?.message}
              </HelperText>
            )}
          </View>
        )}
        name="confirmPassword"
        control={control}
      />

      <Button
        mode="contained"
        style={{ marginTop: 16 }}
        onPress={handleSubmit(onSubmit)}
        loading={formState.isSubmitting}
      >
        Sign up
      </Button>
    </View>
  );
}
