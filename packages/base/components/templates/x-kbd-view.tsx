import { useTheme } from "react-native-paper";
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
} from "react-native";
import type { PropsWithChildren } from "react";

interface XKbdViewProps {
  center?: boolean;
}

export function XKbdView({
  children,
  center,
}: PropsWithChildren<XKbdViewProps>) {
  const { colors } = useTheme();

  return (
    <KeyboardAvoidingView
      enabled
      style={[{ backgroundColor: colors.background }, styles.container]}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <ScrollView
        contentContainerStyle={[
          styles.scrollContainer,
          center && styles.centerContainer,
        ]}
        keyboardShouldPersistTaps="handled"
      >
        {children}
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingVertical: 64,
    gap: 16,
  },
  centerContainer: {
    justifyContent: "center",
    alignItems: "center",
  },
});
