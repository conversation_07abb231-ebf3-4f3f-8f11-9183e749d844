{"name": "@x/rn-shared", "module": "index.ts", "type": "module", "devDependencies": {"@types/bun": "latest"}, "peerDependencies": {"@hookform/resolvers": "^4.1.3", "@tanstack/react-query": "^5.66.3", "expo-location": "^18.0.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.54.2", "react-native": "0.76.7", "react-native-paper": "^5.13.1", "typescript": "^5.0.0", "zod": "^3.24.2", "@x/api": "workspace:*", "@x/auth": "workspace:*"}, "dependencies": {}}