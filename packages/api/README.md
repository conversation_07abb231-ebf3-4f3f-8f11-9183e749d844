# @x/api

This package is responsible for providing all the queries and mutations that the application needs to interact with the
backend application with complete type-safety.

> Note: ApiProvider wraps the application with the QueryClientProvider.

## Structure

- `/src/api.d.ts` - Auto generated types from that swagger using `openapi-typescript` package.
- `/src/api-client.ts` - Client that is responsible for making type-safe requests to the backend.
- `/src/api-provider.tsx` - Provider that wraps the application with the QueryClientProvider. And, it also implements
  the middlewares.
- `/src/query-client.ts` - QueryClient instance that is used to make requests to the backend.
- `/src/queries/**.ts` - All the queries that are used in the application.
- `/src/mutations/**.ts` - All the mutations that are used in the application.
