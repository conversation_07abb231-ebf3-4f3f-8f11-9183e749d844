{"name": "@x/api", "module": "index.ts", "type": "module", "scripts": {"fetch": "openapi-typescript https://tg.sv.devntion.com/docs-json -o ./src/api.d.ts", "fetch:local": "openapi-typescript http://localhost:5000/docs-json -o ./src/api.d.ts", "build": "bun fetch"}, "devDependencies": {"@types/bun": "latest", "openapi-typescript": "^7.6.1"}, "peerDependencies": {"typescript": "^5.0.0", "react": "^19.1.0", "react-dom": "^19.1.0", "@tanstack/react-query": "^5.77.0"}, "dependencies": {"@lukemorales/query-key-factory": "^1.3.4", "@x/constants": "workspace:*", "openapi-fetch": "^0.13.4"}}