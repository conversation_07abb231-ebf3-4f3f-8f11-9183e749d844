// Singleton api class
import createClient from "openapi-fetch";
import { type paths } from "./api";
import { API_URL } from "@x/constants";

export class Api {
  private static defaultClient: ReturnType<typeof createClient<paths>>; // static getInstance(): Api {
  private static unknownClient: ReturnType<typeof createClient<any>>;

  private constructor() {}

  static get default() {
    if (!Api.defaultClient) {
      Api.defaultClient = createClient<paths>({
        baseUrl: API_URL,
      });
    }

    return Api.defaultClient;
  }

  static get unknown() {
    if (!Api.unknownClient) {
      Api.unknownClient = createClient<any>();
    }

    return Api.unknownClient;
  }
}
