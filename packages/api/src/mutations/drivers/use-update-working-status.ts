import { useMutation, useQueryClient } from "@tanstack/react-query";
import type { components } from "../../api";
import { Api } from "../../api-client.ts";
import { queries } from "../../queries";

type UpdateWorkingStatusVariables =
  components["schemas"]["UpdateDriverStatusDto"]["status"];

export function useUpdateWorkingStatus(driverId: string) {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["driver", "update-working-status"],
    mutationFn: async (status: UpdateWorkingStatusVariables) => {
      const res = await Api.default.POST("/drivers/{id}/work-status", {
        params: {
          path: {
            id: driverId,
          },
        },
        body: {
          status,
        },
      });

      return res.data;
    },
    onMutate: async (status) => {
      // optimistic update
      queryClient.setQueryData<components["schemas"]["DriverDto"]>(
        queries.drivers.me.queryKey,
        (oldData) => {
          if (!oldData) return oldData;

          return {
            ...oldData,
            workingStatus: status,
          };
        },
      );
    },
    onSettled: async () => {
      // Invalidate the driver profile query
      // so that the new status is fetched
      // from the server
      await queryClient.invalidateQueries(queries.drivers.me);
    },
  });
}
