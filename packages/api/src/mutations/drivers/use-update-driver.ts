import { useMutation, useQueryClient } from "@tanstack/react-query";
import type { components } from "../../api";
import { Api } from "../../api-client.ts";

// Type for update driver data
export type UpdateDriverVariables = components["schemas"]["UpdateDriverDto"];
export type UpdateDriverInput = {
  updateDriver: Partial<UpdateDriverVariables>;
  driverId: string;
};

export default function useUpdateDriver(driverId: string) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["update-driver", driverId],
    mutationFn: async ({ updateDriver, driverId }: UpdateDriverInput) => {
      try {
        const res = await Api.default.PATCH("/drivers/{id}", {
          params: {
            path: {
              id: driverId,
            },
          },
          body: updateDriver,
        });
        return res.data;
      } catch (error) {}
    },
    onSuccess: (data, { driverId }) => {
      console.log("Driver updated successfully:", data);
      // TODO: update query keys
      // Optimistically update the cache for the specific driver
      queryClient.setQueryData(["driver", driverId], data);
    },
    onError: (error: Error) => {
      console.error("Unexpected error while updating driver:", error);
    },
    onSettled: async (_, __, { driverId }) => {
      // Invalidate the query for the specific driver
      await queryClient.invalidateQueries({ queryKey: ["driver", driverId] });
    },
  });
}
