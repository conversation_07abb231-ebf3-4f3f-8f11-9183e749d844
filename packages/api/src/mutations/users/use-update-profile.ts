import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Api } from "../../api-client.ts";
import type { components } from "../../api";
import { queries } from "../../queries";

export function useUpdateProfile() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: ["updateProfile"],
    mutationFn: async (body: components["schemas"]["UpdateCurrentUserDto"]) => {
      const res = await Api.default.PATCH("/users/me", {
        body,
      });

      return res.data;
    },
    onSettled: async () => {
      void queryClient.invalidateQueries(queries.users.me);
    },
    onMutate: async (data) => {
      // Update the user data in the query cache
      await queryClient.cancelQueries(queries.users.me);
      const previousData = queryClient.getQueryData(queries.users.me.queryKey);

      if (previousData) {
        queryClient.setQueryData(queries.users.me.queryKey, {
          ...previousData,
          ...data,
        });
      }
    },
  });
}
