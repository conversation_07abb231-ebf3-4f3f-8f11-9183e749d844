import { useMutation } from "@tanstack/react-query";
import type { components } from "../../api";
import { Api } from "../../api-client.ts";

export function useRequestRide() {
  return useMutation({
    mutationKey: ["request-ride"],
    mutationFn: async (body: components["schemas"]["RequestRideDto"]) => {
      const res = await Api.default.POST("/rides/request", {
        body,
        parseAs: "text",
      });

      return res.data;
    },
  });
}
