import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Api } from "../../api-client.ts";
import type { components } from "../../api";
import { queries } from "../../queries";

export function useCalculateCost(options?: { fake?: boolean }) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["calculate-cost"],
    mutationFn: async (
      variables: components["schemas"]["TripCostRequestDto"],
    ) => {
      console.log(variables);

      const res = await Api.default.POST("/rides/calculateCost", {
        headers: {
          "x-fake": String(options?.fake),
        },
        body: variables,
      });

      console.log(res.error);

      return res.data;
    },
    onError: (error) => {
      console.error(error, error.message);
    },
    onSuccess: (data) => {
      if (!data) return;
      queryClient.setQueryData(queries.rides.cost(data.id).queryKey, data);
    },
  });
}
