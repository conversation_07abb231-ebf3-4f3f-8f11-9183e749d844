import { useMutation } from "@tanstack/react-query";
import { Api } from "../../api-client.ts";

export default function useAcceptRide() {
  return useMutation({
    mutationKey: ["accept-ride-offer"],
    mutationFn: async (rideId: string) => {
      const res = await Api.default.POST("/rides/acceptRide/{rideId}", {
        params: {
          path: {
            rideId,
          },
        },
      });

      return res.data;
    },
  });
}
