import { useMutation, useQueryClient } from "@tanstack/react-query";
import type { components } from "../../api";
import { Api } from "../../api-client.ts";

export type CreateClientCarVariables = components["schemas"]["CreateClientCarDto"] & {
  isInsured: boolean;
  tripId: string;
};

export function useCreateClientCar() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationKey: ["create-client-car"],
    mutationFn: async ({ isInsured, tripId, ...carData }: CreateClientCarVariables) => {
      const res = await Api.default.POST("/client-cars", {
        params: {
          query: {
            isInsured,
            tripId,
          },
        },
        body: carData,
      });

      return res.data;
    },
    onSuccess: (data) => {
      console.log("Client car created successfully:", data);
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ["client-cars"] });
      if (data?.tripId) {
        queryClient.invalidateQueries({ 
          queryKey: ["client-cars", "by-trip", data.tripId] 
        });
      }
    },
    onError: (error) => {
      console.error("Error creating client car:", error);
    },
  });
}
