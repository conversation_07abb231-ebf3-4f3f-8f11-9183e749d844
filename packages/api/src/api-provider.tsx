import type { Middleware } from "openapi-fetch";
import {
  create<PERSON>ontext,
  type <PERSON>ps<PERSON><PERSON><PERSON><PERSON>dren,
  use<PERSON>ontext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { Api } from "./api-client.ts";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./query-client.ts";

interface IApiContext {
  middlewares: Middleware[];
  setMiddlewares: (middlewares: Middleware[]) => void;
}

const ApiContext = createContext<IApiContext | null>(null);

interface ApiProviderProps {
  fetchToken?: () => Promise<string | undefined> | undefined;
}

export function ApiProvider({
  fetchToken,
  children,
}: PropsWithChildren<ApiProviderProps>) {
  /**
   * Middlewares to add to the API client (optional)
   */
  const [middlewares, setMiddlewares] = useState<Middleware[]>([]);

  useEffect(() => {
    if (middlewares && middlewares.length > 0) {
      middlewares.forEach((middleware) => {
        Api.default.use(middleware);
      });
    }

    return () => {
      middlewares.forEach((middleware) => {
        Api.default.eject(middleware);
      });
    };
  }, [middlewares]);

  /**
   * Auth middleware to add token to requests
   */
  const authMiddleware: Middleware = useMemo(
    () => ({
      onRequest: async ({ request }) => {
        if (!fetchToken) return request;

        const token = await fetchToken();
        if (!token) return request;

        request.headers.set("Authorization", `Bearer ${token}`);
        return request;
      },
    }),
    [fetchToken],
  );

  /**
   * Add auth middleware
   */
  useEffect(() => {
    if (fetchToken) {
      Api.default.use(authMiddleware);
    }

    return () => {
      Api.default.eject(authMiddleware);
    };
  }, [authMiddleware]);

  return (
    <ApiContext.Provider
      value={{
        middlewares,
        setMiddlewares,
      }}
    >
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    </ApiContext.Provider>
  );
}

export function useApiContext() {
  const context = useContext(ApiContext);

  if (!context) {
    throw new Error("useApiContext must be used within an ApiProvider");
  }

  return context;
}
