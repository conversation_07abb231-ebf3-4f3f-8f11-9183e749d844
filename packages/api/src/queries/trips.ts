import { createQuery<PERSON>eys } from "@lukemorales/query-key-factory";
import { Api } from "../api-client.ts";

export const trips = createQueryKeys("trips", {
  customerTrips: (customerId: string) => ({
    queryKey: ["customer", customerId],
    queryFn: async ({ signal }) => {
      const res = await Api.default.GET("/trips/customer/{customerId}", {
        params: {
          path: {
            customerId: customerId,
          },
        },
        signal,
      });
      return res.data;
    },
  }),

  customerTripsMe: {
    queryKey: ["customer", "me"],
    queryFn: async ({ signal }) => {
      const _res = await Api.default.GET("/customer/me", { signal });
      if (!_res.data) return [];

      const res = await Api.default.GET("/trips/customer/{customerId}", {
        params: {
          path: {
            customerId: _res.data?.id,
          },
        },
        signal,
      });

      return res.data;
    },
  },
});
