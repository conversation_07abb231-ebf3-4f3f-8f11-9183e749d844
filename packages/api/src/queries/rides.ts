import { createQueryKeys } from "@lukemorales/query-key-factory";
import { Api } from "../api-client.ts";

interface Ride {
  baseFare: number;
  costPerMeter: number;
  costPerSecond: number;
  currency: string;
  customerId: string;
  destination: string;
  discount: number;
  distanceInMeters: number;
  durationInSeconds: number;
  id: string;
  origin: string;
  paymentMethod: string;
  platformFee: number;
  surgeMultiplier: number;
  tax: number;
  totalFare: number;
  tripType: "TOWING" | "ILLEGAL_PARKING" | "MECHANIC";
}

export const rides = createQueryKeys("rides", {
  detail: (rideId: string) => ({
    queryKey: [rideId],
    queryFn: async ({ signal }) => {
      const res = await Api.default.GET("/rides/getRideDetails/{rideId}", {
        params: {
          path: {
            rideId: rideId,
          },
        },
        signal,
      });

      // TODO: fix this from backend
      return res.data as unknown as Ride;
    },
  }),

  cost: (costId: string) => ({
    queryKey: [costId],
    queryFn: async ({ signal }) => {
      const res = await Api.default.GET("/rides/getRideCost/{calculationId}", {
        params: {
          path: {
            calculationId: costId,
          },
        },
        signal,
      });

      return res.data;
    },
  }),
});
