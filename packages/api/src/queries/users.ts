import { createQueryKeys } from "@lukemorales/query-key-factory";
import { Api } from "../api-client.ts";

export const users = createQueryKeys("users", {
  me: {
    queryKey: null,
    queryFn: async ({ signal }) => {
      const response = await Api.default.GET("/users/me", { signal });
      if (!response.data) throw new Error("User profile not found");

      return response.data;
    },
  },
});
