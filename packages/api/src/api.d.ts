/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */

export interface paths {
    "/": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["AppController_getHello"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/api/test": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["AppController_test"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/locations": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["LocationsController_findAll"];
        put?: never;
        post: operations["LocationsController_create"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/locations/closest-location": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** Getting closest location by type : YARD | MECHANICSHOP | ILLEGAL_PARKING */
        get: operations["LocationsController_getClosestLocation"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/locations/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["LocationsController_findOne"];
        put?: never;
        post?: never;
        delete: operations["LocationsController_remove"];
        options?: never;
        head?: never;
        patch: operations["LocationsController_update"];
        trace?: never;
    };
    "/users": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["UsersController_findAll"];
        put?: never;
        post: operations["UsersController_create"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/users/me": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["UsersController_findCurrentUser"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch: operations["UsersController_updateCurrentUser"];
        trace?: never;
    };
    "/users/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["UsersController_findOne"];
        put?: never;
        post?: never;
        delete: operations["UsersController_remove"];
        options?: never;
        head?: never;
        patch: operations["UsersController_update"];
        trace?: never;
    };
    "/users/AddCard": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["UsersController_addCard"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/users/ListCards": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["UsersController_listCards"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/users/RemoveCard/{cardId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete: operations["UsersController_removeCard"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/users/verify-email": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["UsersController_verifyEmail"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/invoices": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["InvoicesController_findAll"];
        put?: never;
        post: operations["InvoicesController_create"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/invoices/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["InvoicesController_findOne"];
        put?: never;
        post?: never;
        delete: operations["InvoicesController_remove"];
        options?: never;
        head?: never;
        patch: operations["InvoicesController_update"];
        trace?: never;
    };
    "/payments/intent": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create a payment intent
         * @description Creates a Stripe payment intent for a specific invoice
         */
        post: operations["PaymentController_createPaymentIntent"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/payments/pre-authorize": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Pre-authorize a payment for a trip
         * @description Authorizes a payment for a trip without capturing it immediately. This reserves the funds on the customer's card.
         */
        post: operations["PaymentController_preAuthorizeTripPayment"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/payments/capture": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Capture a pre-authorized payment
         * @description Captures a previously authorized payment with the final amount. If the final amount exceeds the pre-authorized amount, it cancels the authorization and creates a new payment.
         */
        post: operations["PaymentController_captureTripPayment"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/payments/cancel": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Cancel a pre-authorized payment
         * @description Cancels a previously authorized payment, releasing the hold on the customer's funds.
         */
        post: operations["PaymentController_cancelTripPreAuthorization"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/payments/customers": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create a Stripe customer
         * @description Creates a new customer in Stripe with the provided details.
         */
        post: operations["PaymentController_createStripeCustomer"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/payments/customers/{customerId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        /**
         * Update a Stripe customer
         * @description Updates an existing customer in Stripe with the provided details.
         */
        patch: operations["PaymentController_updateStripeCustomer"];
        trace?: never;
    };
    "/payments/customers/{customerId}/cards": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List payment methods for a customer
         * @description Retrieves all payment methods (cards) associated with a Stripe customer.
         */
        get: operations["PaymentController_listCardsForStripeCustomer"];
        put?: never;
        /**
         * Add a payment method to a customer
         * @description Attaches a payment method to a Stripe customer for future payments.
         */
        post: operations["PaymentController_attachCardsWithStripeCustomer"];
        /**
         * Delete a payment method
         * @description Removes a payment method (card) from a Stripe customer.
         */
        delete: operations["PaymentController_deleteCardForStripeCustomer"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/payments/portal/sessions": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /**
         * Create a customer portal session
         * @description Creates a Stripe Customer Portal session where customers can manage their payment methods and billing details.
         */
        post: operations["PaymentController_createCustomerPortalSession"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/payments/portal/configurations": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /**
         * List customer portal configurations
         * @description Retrieves all customer portal configurations in your Stripe account.
         */
        get: operations["PaymentController_listCustomerPortalConfigurations"];
        put?: never;
        /**
         * Create a customer portal configuration
         * @description Creates a configuration for the Stripe Customer Portal, customizing its appearance and functionality.
         */
        post: operations["PaymentController_createCustomerPortalConfiguration"];
        delete?: never;
        options?: never;
        head?: never;
        /**
         * Update a customer portal configuration
         * @description Updates an existing configuration for the Stripe Customer Portal.
         */
        patch: operations["PaymentController_updateCustomerPortalConfiguration"];
        trace?: never;
    };
    "/stripe/webhook": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["StripeWebhookController_handleWebhook"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rides/calculateCost": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["RidesController_preRideCostCalculation"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rides/getRideCost/{calculationId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["RidesController_getRideCalculationById"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rides/request": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["RidesController_request"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rides/acceptRide/{rideId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["RidesController_acceptRide"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rides/getRideDetails/{rideId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["RidesController_getRideDetails"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rides/completeRide": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["RidesController_completeRide"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/rides/chargeCustomer/{tripId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["RidesController_chargeCustomer"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/settings": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["SettingsController_findAll"];
        put?: never;
        post: operations["SettingsController_create"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/settings/TripTypes": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["SettingsController_findAllTypes"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/settings/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["SettingsController_findOne"];
        put?: never;
        post?: never;
        delete: operations["SettingsController_remove"];
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/settings/type/{type}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["SettingsController_findByType"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/settings/{type}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch: operations["SettingsController_update"];
        trace?: never;
    };
    "/trips": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["TripsController_findAll"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/trips/total": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["TripsController_getTotalTrips"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/trips/recent": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["TripsController_getRecentTrips"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/trips/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["TripsController_findOne"];
        put?: never;
        post?: never;
        delete: operations["TripsController_remove"];
        options?: never;
        head?: never;
        patch: operations["TripsController_update"];
        trace?: never;
    };
    "/trips/customer/{customerId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["TripsController_findTripsByCustomerId"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/trips/driver/{driverId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["TripsController_findTripsByDriverId"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/trips/rate": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        /** Rate a completed trip and add it to the driver's rating */
        post: operations["TripsController_rateTrip"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/drivers/register": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["DriversController_register"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/drivers/register-admin": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["DriversController_registerAdmin"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/drivers": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["DriversController_findAll"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/drivers/me": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["DriversController_findCurrentDriver"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/drivers/total": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["DriversController_total"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/drivers/findClosesDriver": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["DriversController_findClosesDriver"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/drivers/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["DriversController_findOne"];
        put?: never;
        post?: never;
        delete: operations["DriversController_remove"];
        options?: never;
        head?: never;
        patch: operations["DriversController_update"];
        trace?: never;
    };
    "/drivers/status/{statusType}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["DriversController_findByStatus"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/drivers/{id}/work-status": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["DriversController_updateWorkStatus"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/trucks": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["TrucksController_findAll"];
        put?: never;
        post: operations["TrucksController_create"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/trucks/create": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get?: never;
        put?: never;
        post: operations["TrucksController_createAppTruck"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/trucks/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["TrucksController_findOne"];
        put?: never;
        post?: never;
        delete: operations["TrucksController_remove"];
        options?: never;
        head?: never;
        patch: operations["TrucksController_update"];
        trace?: never;
    };
    "/client-cars": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["ClientCarController_findAll"];
        put?: never;
        post: operations["ClientCarController_create"];
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client-cars/by-trip/{tripId}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["ClientCarController_findByTripId"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/client-cars/{id}": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["ClientCarController_findOne"];
        put?: never;
        post?: never;
        delete: operations["ClientCarController_remove"];
        options?: never;
        head?: never;
        patch: operations["ClientCarController_update"];
        trace?: never;
    };
    "/dashboard": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["DashboardController_getTotal"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/dashboard/TermsAndConditions": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["DashboardController_getTermsAndConditions"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/dashboard/revenue": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["DashboardController_getRevenueStatistics"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
    "/customer/me": {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        get: operations["CustomerController_findCurrentUser"];
        put?: never;
        post?: never;
        delete?: never;
        options?: never;
        head?: never;
        patch?: never;
        trace?: never;
    };
}
export type webhooks = Record<string, never>;
export interface components {
    schemas: {
        CreateLocationDto: {
            /**
             * @description Name of the location
             * @example Downtown Garage
             */
            name: string;
            /**
             * @description Description of the location
             * @example Main storage facility
             */
            description?: string;
            /**
             * @description Capacity of the location
             * @example 50
             */
            capacity: number;
            /**
             * @description Address of the location
             * @example 123 Main St
             */
            address?: string;
            /**
             * @description Latitude of the location
             * @example 37.7749
             */
            latitude: number;
            /**
             * @description Longitude of the location
             * @example -122.4194
             */
            longitude: number;
            /**
             * @description Type of the location
             * @example YARD | MECHANICSHOP | ILLEGAL_PARKING
             * @enum {string}
             */
            type: "YARD" | "MECHANICSHOP" | "ILLEGAL_PARKING";
        };
        LocationDto: {
            /**
             * @description ID of the location
             * @example 1
             */
            id: string;
            /**
             * @description Name of the location
             * @example Garage 1
             */
            name: string;
            /**
             * @description Description of the location
             * @example This is a description
             */
            description: string;
            /**
             * @description Address of the location
             * @example 123 Main St
             */
            address: string;
            /**
             * @description Longitude of the location
             * @example -122.084
             */
            longitude: number;
            /**
             * @description Latitude of the location
             * @example 37.422
             */
            latitude: number;
            /**
             * @description Capacity of the location
             * @example 100
             */
            capacity: number;
            /**
             * @description Type of the location
             * @example YARD | MECHANICSHOP | ILLEGAL_PARKING
             */
            type: string;
            /**
             * Format: date-time
             * @description Created at timestamp
             * @example 2021-03-02T20:12:00.000Z
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Updated at timestamp
             * @example 2021-03-02T20:12:00.000Z
             */
            updatedAt: string;
            /**
             * @description Distance from the location
             * @example 0.5
             */
            distance: number;
        };
        PageMetaDto: {
            page: number;
            take: number;
            itemCount: number;
            pageCount: number;
            hasNextPage: boolean;
            hasPreviousPage: boolean;
        };
        PageDto: {
            data: unknown[][];
            meta: components["schemas"]["PageMetaDto"];
        };
        UpdateLocationDto: {
            /**
             * @description Name of the location
             * @example Downtown Garage
             */
            name?: string;
            /**
             * @description Description of the location
             * @example Main storage facility
             */
            description?: string;
            /**
             * @description Capacity of the location
             * @example 50
             */
            capacity?: number;
            /**
             * @description Address of the location
             * @example 123 Main St
             */
            address?: string;
            /**
             * @description Latitude of the location
             * @example 37.7749
             */
            latitude?: number;
            /**
             * @description Longitude of the location
             * @example -122.4194
             */
            longitude?: number;
            /**
             * @description Type of the location
             * @example YARD | MECHANICSHOP | ILLEGAL_PARKING
             * @enum {string}
             */
            type?: "YARD" | "MECHANICSHOP" | "ILLEGAL_PARKING";
        };
        RegisterUserDto: {
            /** @example <EMAIL> */
            email: string;
            /** @example John */
            firstName: string;
            /** @example snow */
            lastName: string;
            /** @example Insurance Company */
            insuranceCompany?: string;
            /** @example 1234567890 */
            insurancePolicyNumber?: string;
            /** @example https://exampilepic.com */
            insurancePolicyFile?: string;
            /**
             * @example FULL
             * @enum {string}
             */
            insuranceType?: "FULL" | "PARTIAL";
            /** @example https://exampilepic.com */
            profilePic: string;
            /**
             * @description Mobile number of the user
             * @example +************
             */
            mobile: string;
        };
        UserDto: {
            /** @description The id of the user */
            id: string;
            /** @description The firebase uuid of the user */
            uid: string;
            /** @description The email of the user */
            email: string;
            /** @description The first name of the user */
            firstName?: string;
            /** @description The last name of the user */
            lastName?: string;
            /** @description The roles of the user */
            roles: ("CUSTOMER" | "DRIVER" | "ADMIN" | "MANAGER" | "STAFF" | "POLICE")[];
            /** @description The created at date of the user */
            createdAt: string;
            /** @description Last updated at date of the user */
            updatedAt: string;
            /** @description The profile picture of the user */
            profilePic?: string;
            /**
             * @description The mobile number of the user
             * @example +************
             */
            mobile?: string;
        };
        UpdateCurrentUserDto: {
            /** @example John */
            firstName?: string;
            /** @example snow */
            lastName?: string;
            /** @example Insurance Company */
            insuranceCompany?: string;
            /** @example 1234567890 */
            insurancePolicyNumber?: string;
            /** @example https://exampilepic.com */
            insurancePolicyFile?: string;
            /**
             * @example FULL
             * @enum {string}
             */
            insuranceType?: "FULL" | "PARTIAL";
            /** @example https://exampilepic.com */
            profilePic?: string;
            /**
             * @description Mobile number of the user
             * @example +************
             */
            mobile?: string;
        };
        UpdateUserRoleDto: {
            /** @example John */
            firstName?: string;
            /** @example snow */
            lastName?: string;
            /**
             * @description New roles to assign to the user
             * @example [
             *       "DRIVER"
             *     ]
             */
            roles?: ("CUSTOMER" | "DRIVER" | "ADMIN" | "MANAGER" | "STAFF" | "POLICE")[];
            /** @example https://exampilepic.com */
            profilePic?: string;
            /**
             * @description Date of Birth
             * @example 1990-01-01
             */
            dob?: string;
            /**
             * @description Driving Licence
             * @example DL12345
             */
            drivingLicence?: string;
            /**
             * @description Transport Licence
             * @example TL67890
             */
            transportLicence?: string;
            /**
             * @description Address
             * @example 123 Main St
             */
            address?: string;
            /**
             * @description Mobile
             * @example +1234567890
             */
            mobile?: string;
        };
        UpdateUserDto: {
            /** @example <EMAIL> */
            email?: string;
            /** @example John */
            firstName?: string;
            /** @example snow */
            lastName?: string;
            /** @example Insurance Company */
            insuranceCompany?: string;
            /** @example 1234567890 */
            insurancePolicyNumber?: string;
            /** @example https://exampilepic.com */
            insurancePolicyFile?: string;
            /**
             * @example FULL
             * @enum {string}
             */
            insuranceType?: "FULL" | "PARTIAL";
            /** @example https://exampilepic.com */
            profilePic?: string;
            /**
             * @description Mobile number of the user
             * @example +************
             */
            mobile?: string;
        };
        EmailDto: {
            /**
             * @description Email of the user
             * @example <EMAIL>
             */
            email: string;
        };
        CreateInvoiceDto: {
            /**
             * @description Trip ID
             * @example 13e8dcab-f134-4d56-b952-0a82f75b8344
             */
            tripId: string;
            /**
             * @description Base fare for the trip
             * @example 31
             */
            baseFare: number;
            /**
             * @description Fare per kilometer
             * @example 10
             */
            farePerKm: number;
            /**
             * @description Fare per minute
             * @example 5
             */
            farePerMin: number;
            /**
             * @description Cancellation fee
             * @example 5
             */
            cancellation: number;
            /**
             * @description Extra fare
             * @example 5
             */
            extraFare: number;
            /**
             * @description Currency
             * @example [
             *       "USD",
             *       "AUD",
             *       "NZD"
             *     ]
             * @enum {string}
             */
            currency: "NZD" | "AUD" | "USD";
            /**
             * @description Tax amount
             * @example 5
             */
            tax: number;
            /**
             * @description Platform fee
             * @example 5
             */
            platformFee: number;
            /**
             * @description Discount amount
             * @example 5
             */
            discountAmount: number;
            /**
             * @description Surge multiplier
             * @example 1.5
             */
            surgeMultiplier: number;
            /**
             * @description Total amount
             * @example 100
             */
            totalAmount: number;
            /**
             * @description Payment method
             * @example CASH | CARD | OTHER
             * @enum {string}
             */
            paymentMethod: "CASH" | "CARD" | "OTHER";
            /**
             * @description Payment status
             * @example PAID | PENDING | REFUNDED | FAILED
             * @enum {string}
             */
            paymentStatus: "PAID" | "PENDING" | "FAILED" | "REFUNDED";
        };
        UpdateInvoiceDto: Record<string, never>;
        CreatePaymentIntentDto: {
            /**
             * @description Invoice ID to create payment intent for
             * @example a1b2c3d4-e5f6-7890-abcd-ef1234567890
             */
            invoiceId: string;
        };
        PreAuthorizeTripPaymentDto: {
            /**
             * @description Stripe customer ID
             * @example cus_1234567890
             */
            customerId: string;
            /**
             * @description Estimated amount in cents
             * @example 2500
             */
            estimatedAmount: number;
            /**
             * @description Currency code
             * @example usd
             */
            currency: string;
        };
        CaptureTripPaymentDto: {
            /**
             * @description Payment intent ID
             * @example pi_1234567890
             */
            paymentIntentId: string;
            /**
             * @description Final amount in cents
             * @example 2250
             */
            finalAmount: number;
        };
        CancelPaymentDto: {
            /**
             * @description Payment intent ID
             * @example pi_1234567890
             */
            paymentIntentId: string;
        };
        CreateStripeCustomerDto: {
            /**
             * @description Customer name
             * @example John Doe
             */
            name: string;
            /**
             * @description Customer email
             * @example <EMAIL>
             */
            email: string;
            /**
             * @description User ID in the system
             * @example a1b2c3d4-e5f6-7890-abcd-ef1234567890
             */
            userId: string;
        };
        UpdateStripeCustomerDto: {
            /**
             * @description Customer name
             * @example John Doe
             */
            name: string;
        };
        AttachCardDto: {
            /**
             * @description Payment method ID
             * @example pm_1234567890
             */
            paymentMethodId: string;
        };
        DeleteCardDto: {
            /**
             * @description Card ID
             * @example card_1234567890
             */
            cardId: string;
        };
        CustomerPortalSessionDto: {
            /**
             * @description Return URL after session
             * @example https://example.com/account
             */
            returnUrl: string;
        };
        CustomerPortalConfigurationDto: {
            /**
             * @description Business name
             * @example ACME Inc.
             */
            businessName: string;
            /**
             * @description Privacy policy URL
             * @example https://example.com/privacy
             */
            privacyPolicyUrl: string;
            /**
             * @description Terms of service URL
             * @example https://example.com/terms
             */
            termsOfServiceUrl: string;
        };
        UpdatePortalConfigurationDto: {
            /**
             * @description Business name
             * @example ACME Inc.
             */
            businessName: string;
            /**
             * @description Privacy policy URL
             * @example https://example.com/privacy
             */
            privacyPolicyUrl: string;
            /**
             * @description Terms of service URL
             * @example https://example.com/terms
             */
            termsOfServiceUrl: string;
            /**
             * @description Configuration ID
             * @example bpc_1234567890
             */
            configurationId: string;
        };
        TripCostRequestDto: {
            /**
             * @description Origin
             * @example 31.5838086,74.4709953
             */
            origin: string;
            /**
             * @description Destination
             * @example 31.5838086,74.4709953
             */
            destination: string;
            /**
             * @description Ride type
             * @example MECHANIC
             * @enum {string}
             */
            TripType: "ILLEGAL_PARKING" | "TOWING" | "MECHANIC";
        };
        RideCalculationDto: {
            /**
             * @description Unique identifier of the trip
             * @example 123e4567-e89b-12d3-a456-426614174000
             */
            id: string;
            /**
             * @description Base fare for the trip
             * @example 31
             */
            baseFare: number;
            /**
             * @description Cost per kilometer
             * @example 10
             */
            costPerMeter: number;
            /**
             * @description Cost per minute
             * @example 5
             */
            costPerSecond: number;
            /**
             * @description Surge multiplier
             * @example 1.5
             */
            surgeMultiplier: number;
            /**
             * @description Discount amount
             * @example 5
             */
            discount: number;
            /**
             * @description Tax amount
             * @example 5
             */
            tax: number;
            /**
             * @description Fee for using the platform
             * @example 5
             */
            platformFee: number;
            /**
             * @description Total Time in seconds
             * @example 100
             */
            durationInSeconds: number;
            /**
             * @description Total Distance in meters
             * @example 1000
             */
            distanceInMeters: number;
            /**
             * @description Total Amount
             * @example 100
             */
            totalFare: number;
            /**
             * @description Currency
             * @example USD
             */
            currency: string;
            /**
             * @description Type of the trip
             * @example ILLEGAL_PARKING | TOWING | MECHANIC
             * @enum {string}
             */
            tripType: "ILLEGAL_PARKING" | "TOWING" | "MECHANIC";
            /**
             * Format: date-time
             * @description Date of the calculation
             * @example 2021-09-30T00:00:00Z
             */
            createdAt: string;
            /** @description Ride details */
            ride: components["schemas"]["TripCostRequestDto"];
        };
        RequestRideDto: {
            /** @description The id of the calculation */
            calculationId: string;
            /** @description The id of the user who is requesting the ride */
            userId: string;
            /**
             * @description Payment Method
             * @enum {string}
             */
            paymentMethod: "CASH" | "CARD" | "OTHER";
        };
        CompleteRideDto: {
            /**
             * @description Id of the ride
             * @example 123
             */
            tripId: string;
            /**
             * @description Latitude and Longitude of the drop off location
             * @example 33.6844,73.0478
             */
            dropOfLocation: string;
            /**
             * @description Status of the trip
             * @example CREATED | STARTED | COMPLETED | CANCELLED
             * @enum {string}
             */
            status: "PENDING" | "ACCEPTED" | "REJECTED" | "CANCELLED" | "COMPLETED";
        };
        CreateSettingDto: {
            /**
             * @description Base fare for illegal parking
             * @example 0
             */
            ILLIGLEPARKING_baseFare: number;
            /**
             * @description Cost per kilometer for illegal parking
             * @example 0
             */
            ILLIGLEPARKING_farePerKm: number;
            /**
             * @description Cost per minute for illegal parking
             * @example 0
             */
            ILLIGLEPARKING_farePerMin: number;
            /**
             * @description Cancellation fee for illegal parking
             * @example 0
             */
            ILLIGLEPARKING_cancellation: number;
            /**
             * @description Extra fare for illegal parking
             * @example 0
             */
            ILLIGLEPARKING_extraFare: number;
            /**
             * @description Currency for illegal parking fees
             * @example USD
             * @enum {string}
             */
            ILLIGLEPARKING_currency: "NZD" | "AUD" | "USD";
            /**
             * @description Tax for illegal parking
             * @example 0
             */
            ILLIGLEPARKING_tax: number;
            /**
             * @description Base fare for mechanic services
             * @example 0
             */
            MECHANIC_baseFare: number;
            /**
             * @description Cost per kilometer for mechanic services
             * @example 0
             */
            MECHANIC_farePerKm: number;
            /**
             * @description Cost per minute for mechanic services
             * @example 0
             */
            MECHANIC_farePerMin: number;
            /**
             * @description Cancellation fee for mechanic services
             * @example 0
             */
            MECHANIC_cancellation: number;
            /**
             * @description Extra fare for mechanic services
             * @example 0
             */
            MECHANIC_extraFare: number;
            /**
             * @description Currency for mechanic service fees
             * @example USD
             * @enum {string}
             */
            MECHANIC_currency: "NZD" | "AUD" | "USD";
            /**
             * @description Tax for mechanic services
             * @example 0
             */
            MECHANIC_tax: number;
            /**
             * @description Base fare for towing
             * @example 0
             */
            TOWING_baseFare: number;
            /**
             * @description Cost per kilometer for towing
             * @example 0
             */
            TOWING_farePerKm: number;
            /**
             * @description Cost per minute for towing
             * @example 0
             */
            TOWING_farePerMin: number;
            /**
             * @description Cancellation fee for towing
             * @example 0
             */
            TOWING_cancellation: number;
            /**
             * @description Extra fare for towing
             * @example 0
             */
            TOWING_extraFare: number;
            /**
             * @description Currency for towing fees
             * @example USD
             * @enum {string}
             */
            TOWING_currency: "NZD" | "AUD" | "USD";
            /**
             * @description Tax for towing
             * @example 0
             */
            TOWING_tax: number;
            /**
             * @description Platform Fee
             * @example 123
             */
            platformFee: number;
        };
        SettingsDto: {
            /**
             * @description ID of the setting
             * @example
             */
            id: string;
            /**
             * @description Base fare for illegal parking
             * @example 0
             */
            ILLIGLEPARKING_baseFare: number;
            /**
             * @description Cost per kilometer for illegal parking
             * @example 0
             */
            ILLIGLEPARKING_farePerKm: number;
            /**
             * @description Cost per minute for illegal parking
             * @example 0
             */
            ILLIGLEPARKING_farePerMin: number;
            /**
             * @description Cancellation fee for illegal parking
             * @example 0
             */
            ILLIGLEPARKING_cancellation: number;
            /**
             * @description Extra fare for illegal parking
             * @example 0
             */
            ILLIGLEPARKING_extraFare: number;
            /**
             * @description Currency for illegal parking fees
             * @example USD
             * @enum {string}
             */
            ILLIGLEPARKING_currency: "NZD" | "AUD" | "USD";
            /**
             * @description Tax for illegal parking
             * @example 0
             */
            ILLIGLEPARKING_tax: number;
            /**
             * @description Base fare for mechanic services
             * @example 0
             */
            MECHANIC_baseFare: number;
            /**
             * @description Cost per kilometer for mechanic services
             * @example 0
             */
            MECHANIC_farePerKm: number;
            /**
             * @description Cost per minute for mechanic services
             * @example 0
             */
            MECHANIC_farePerMin: number;
            /**
             * @description Cancellation fee for mechanic services
             * @example 0
             */
            MECHANIC_cancellation: number;
            /**
             * @description Extra fare for mechanic services
             * @example 0
             */
            MECHANIC_extraFare: number;
            /**
             * @description Currency for mechanic service fees
             * @example USD
             * @enum {string}
             */
            MECHANIC_currency: "NZD" | "AUD" | "USD";
            /**
             * @description Tax for mechanic services
             * @example 0
             */
            MECHANIC_tax: number;
            /**
             * @description Base fare for towing
             * @example 0
             */
            TOWING_baseFare: number;
            /**
             * @description Cost per kilometer for towing
             * @example 0
             */
            TOWING_farePerKm: number;
            /**
             * @description Cost per minute for towing
             * @example 0
             */
            TOWING_farePerMin: number;
            /**
             * @description Cancellation fee for towing
             * @example 0
             */
            TOWING_cancellation: number;
            /**
             * @description Extra fare for towing
             * @example 0
             */
            TOWING_extraFare: number;
            /**
             * @description Currency for towing fees
             * @example USD
             * @enum {string}
             */
            TOWING_currency: "NZD" | "AUD" | "USD";
            /**
             * @description Tax for towing
             * @example 0
             */
            TOWING_tax: number;
            /**
             * @description Platform Fee
             * @example 123
             */
            platformFee: number;
            /**
             * Format: date-time
             * @description Created at timestamp
             * @example 2021-03-02T20:12:00.000Z
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Updated at timestamp
             * @example 2021-03-02T20:12:00.000Z
             */
            updatedAt: string;
        };
        TripSettingsDto: {
            /**
             * @description ID of the trip type settings
             * @example
             */
            id: string;
            /**
             * @description Base fare for the trip type
             * @example 0
             */
            baseFare: number;
            /**
             * @description Cost per kilometer for the trip type
             * @example 0
             */
            farePerKm: number;
            /**
             * @description Cost per minute for the trip type
             * @example 0
             */
            farePerMin: number;
            /**
             * @description Cancellation fee for the trip type
             * @example 0
             */
            cancellation: number;
            /**
             * @description Extra fare for the trip type
             * @example 0
             */
            extraFare: number;
            /**
             * @description Currency for the trip type fees
             * @example USD
             * @enum {string}
             */
            currency: "NZD" | "AUD" | "USD";
            /**
             * @description Tax for the trip type
             * @example 0
             */
            tax: number;
            /**
             * @description Platform fee for the trip type
             * @example 0
             */
            platformFee: number;
        };
        UpdatePriceDto: {
            /**
             * @description Base fare
             * @example 0
             */
            baseFare: number;
            /**
             * @description Cost per kilometer
             * @example 0
             */
            farePerKm: number;
            /**
             * @description Cost per minute
             * @example 0
             */
            farePerMin: number;
            /**
             * @description Cancellation fee
             * @example 0
             */
            cancellation: number;
            /**
             * @description Extra fare
             * @example 0
             */
            extraFare: number;
            /**
             * @description Currency for illegal parking fees
             * @example USD
             * @enum {string}
             */
            currency: "NZD" | "AUD" | "USD";
            /**
             * @description Tax for illegal parking
             * @example 0
             */
            tax: number;
            /**
             * @description Platform fee for illegal parking
             * @example 0
             */
            platformFee: number;
        };
        RecentTripDto: {
            /**
             * @description Trip ID
             * @example 123e4567-e89b-12d3-a456-426614174000
             */
            id: string;
            /**
             * @description Trip status
             * @example COMPLETED
             * @enum {string}
             */
            status: "PENDING" | "ACCEPTED" | "REJECTED" | "CANCELLED" | "COMPLETED";
            /**
             * @description Trip from location
             * @example -36.848461,174.763336
             */
            from: string;
            /**
             * @description Trip to location
             * @example -36.852461,174.768336
             */
            to: string;
            /**
             * @description Trip from address
             * @example 123 Queen Street, Auckland, New Zealand
             */
            fromAddress: string;
            /**
             * @description Trip to address
             * @example 456 Victoria Street, Auckland, New Zealand
             */
            toAddress: string;
            /**
             * Format: date-time
             * @description Trip creation date
             * @example 2023-01-01T00:00:00.000Z
             */
            createdAt: string;
            /**
             * @description Driver name
             * @example John Doe
             */
            driverName: string;
            /**
             * @description Driver email
             * @example <EMAIL>
             */
            driverEmail: string;
            /**
             * @description Driver profile image
             * @example https://example.com/profile.jpg
             */
            driverAvatar: string;
            /**
             * @description Driver revenue from this trip
             * @example 120
             */
            driverRevenue: number;
        };
        TripDto: {
            /**
             * @description Unique identifier of the trip
             * @example 12121-12121-12121-12121
             */
            id: string;
            /**
             * @description Customer id provided by google identity provider
             * @example i59pTszPnTY12nF1ErZCU9bbJV52
             */
            customerId: string;
            /**
             * @description Driver id provided by google identity provider
             * @example i59pTszPnTY12nF1ErZCU9bbJV52
             */
            driverId: string;
            /**
             * @description Distance in meters
             * @example 10
             */
            distanceInMeters: number;
            /**
             * @description Duration in seconds
             * @example 100
             */
            durationInSeconds: number;
            /**
             * @description Latitude and Longitude of the pickup location
             * @example 33.6844,73.0479
             */
            from: string;
            /**
             * @description Latitude and Longitude of the drop off location
             * @example 33.6844,73.0478
             */
            to: string;
            /**
             * @description Status of the trip
             * @enum {string}
             */
            status: "PENDING" | "ACCEPTED" | "REJECTED" | "CANCELLED" | "COMPLETED";
            /**
             * @description Type of the trip
             * @enum {string}
             */
            type: "ILLEGAL_PARKING" | "TOWING" | "MECHANIC";
            /**
             * @description Invoice ID associated with the trip
             * @example 12121-12121-12121-12121
             */
            invoiceId: string | null;
            /**
             * @description Total charges for the trip in cents
             * @default 0
             * @example 2500
             */
            charges: number;
            /**
             * Format: date-time
             * @description Date and time of the trip creation
             * @example 2022-01-01T00:00:00Z
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Date and time of the trip last update
             * @example 2022-01-01T00:00:00Z
             */
            updatedAt: string;
        };
        UpdateTripDto: Record<string, never>;
        CreateRatingDto: {
            /**
             * @description Rating score (0.0 to 5.0)
             * @example 4.5
             */
            score: number;
            /**
             * @description Trip ID
             * @example 123e4567-e89b-12d3-a456-426614174000
             */
            tripId: string;
        };
        RegisterDriverDto: {
            /**
             * @description First name of the driver
             * @example John
             */
            firstName: string;
            /**
             * @description Last name of the driver
             * @example Doe
             */
            lastName: string;
            /**
             * @description Email of the driver
             * @example <EMAIL>
             */
            email: string;
            /**
             * @description Password of the driver
             * @example 123456
             */
            password?: string;
            /**
             * @description Mobile number of the driver
             * @example +9234567890
             */
            mobile: string;
        };
        File: Record<string, never>;
        DriverDto: {
            /**
             * @description ID of the driver
             * @example 1
             */
            id: string;
            /**
             * Format: date-time
             * @description Date of birth of the driver
             * @example 1990-01-01T12:00:00Z
             */
            dob: string;
            /**
             * @description Driving licence number of the driver
             * @example 123456789
             */
            drivingLicence: string;
            /**
             * @description Transport licence number of the driver
             * @example 123456789
             */
            transportLicence: string;
            /**
             * @description Address of the driver
             * @example 123 Main St
             */
            address: string;
            /**
             * @deprecated
             * @description Mobile number of the driver (DEPRECATED - use user.mobile instead)
             * @example +************
             */
            mobile: string;
            /**
             * @description ID of the truck assigned to the driver
             * @example 1
             */
            truckId: string;
            /**
             * @description Average rating of the driver
             * @example 4.5
             */
            avgRating?: number;
            /**
             * @description Status of the driver
             * @example ACTIVE
             * @enum {string}
             */
            status: "ACTION_REQUIRED" | "ACTIVE" | "REJECTED" | "PENDING" | "INACTIVE";
            /**
             * @description Description of the status
             * @example Insurance papers are missing
             */
            statusDesc: string;
            /**
             * @description ID of the user
             * @example 1
             */
            userId: string;
            /**
             * @description Working status of the driver
             * @example OFFLINE
             * @enum {string}
             */
            workingStatus: "ONLINE" | "BUSY" | "OFFLINE";
            /**
             * Format: date-time
             * @description Created at timestamp
             * @example 2021-03-02T20:12:00.000Z
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Updated at timestamp
             * @example 2021-03-02T20:12:00.000Z
             */
            updatedAt: string;
            /** @description Files uploaded by the driver */
            files: components["schemas"]["File"][];
            /** @description The user record */
            user?: components["schemas"]["UserDto"];
        };
        RegisterDriverAdminDto: {
            /**
             * @description First name of the driver
             * @example John
             */
            firstName: string;
            /**
             * @description Last name of the driver
             * @example Doe
             */
            lastName: string;
            /**
             * Format: date-time
             * @example 2023-09-01T12:00:00Z
             */
            dob: string;
            /** @example 123-3456-1 */
            drivingLicenseNumber?: string;
            /** @example 123-3456-1 */
            transportLicenseNumber?: string;
            /** @example Lahore, Pakistan */
            address?: string;
            /** @example +************ */
            mobile: string;
            /** @example <EMAIL> */
            email: string;
            /** @example https://example.com/profile.jpg */
            profilePicture: string;
            /** @example https://example.com/id-card_front.jpg */
            idCard_front: string;
            /** @example https://example.com/id-card_back.jpg */
            idCard_back: string;
            /** @example https://example.com/driving-license.jpg */
            drivingLicense: string;
            /** @example https://example.com/transport-license.jpg */
            transportLicense: string;
        };
        UpdateDriverDto: {
            /**
             * Format: date-time
             * @description Date of birth of the driver
             * @example 1990-01-01T12:00:00Z
             */
            dob?: string;
            /**
             * @description Driving licence number of the driver
             * @example 123456789
             */
            drivingLicence?: string;
            /**
             * @description Transport licence number of the driver
             * @example 123456789
             */
            transportLicence?: string;
            /**
             * @description Address of the driver
             * @example 123 Main St
             */
            address?: string;
            /**
             * @deprecated
             * @description Mobile number of the user
             * @example +************
             */
            mobile?: string;
            /**
             * @description ID of the truck assigned to the driver
             * @example 1
             */
            truckId?: string;
            /**
             * @description Average rating of the driver
             * @example 4.5
             */
            avgRating?: number;
            /**
             * @description Status of the driver
             * @example ACTIVE
             * @enum {string}
             */
            status?: "ACTION_REQUIRED" | "ACTIVE" | "REJECTED" | "PENDING" | "INACTIVE";
            /**
             * @description Description of the status
             * @example Insurance papers are missing
             */
            statusDesc?: string;
            /**
             * @description Working status of the driver
             * @example OFFLINE
             * @enum {string}
             */
            workingStatus?: "ONLINE" | "BUSY" | "OFFLINE";
            /**
             * Format: date-time
             * @description Created at timestamp
             * @example 2021-03-02T20:12:00.000Z
             */
            createdAt?: string;
            /**
             * Format: date-time
             * @description Updated at timestamp
             * @example 2021-03-02T20:12:00.000Z
             */
            updatedAt?: string;
            /** @description Files uploaded by the driver */
            files?: components["schemas"]["File"][];
            /** @description The user record */
            user?: components["schemas"]["UserDto"];
            /** @example https://example.com/profile.jpg */
            ProfilePic?: string;
            /** @example https://example.com/idcard_front.jpg */
            idCard_front?: string;
            /** @example https://example.com/idcard_back.jpg */
            idCard_back?: string;
            /** @example https://example.com/registration.jpg */
            registration?: string;
            /** @example https://example.com/TransportLicense.jpg */
            transportLicencePic?: string;
            /** @example https://example.com/DrivingLicense.jpg */
            DrivingLicencePic?: string;
            /** @example John */
            firstName?: string;
            /** @example Doe */
            lastName?: string;
        };
        UpdateDriverStatusDto: {
            /**
             * @example ONLINE
             * @enum {string}
             */
            status: "ONLINE" | "OFFLINE";
        };
        CreateTruckDto: {
            /**
             * @description Capacity of the truck in tons
             * @example 24
             */
            capacity: number;
            /**
             * @description Manufacturer of the truck
             * @example Mercedes
             */
            make: string;
            /**
             * @description Model of the truck
             * @example Actros
             */
            model: string;
            /**
             * @description Registration Number of the truck
             * @example ABC-123
             */
            registration: string;
            /**
             * Format: date-time
             * @description Registration Expiry Date of the truck
             * @example 2025-03-02T20:12:00.000Z
             */
            registrationExpiry: string;
            /**
             * @description Status of the truck
             * @example ACTIVE | INACTIVE
             * @enum {string}
             */
            status: "ACTIVE" | "INACTIVE";
            /**
             * @description Type of the truck
             * @example STANDARD | SLIDING | TOW
             * @enum {string}
             */
            type: "STANDARD" | "SLIDING" | "TOW";
            /** @example https://example.com/front.jpg */
            front: string;
            /** @example https://example.com/back.jpg */
            back: string;
            /** @example https://example.com/left.jpg */
            left: string;
            /** @example https://example.com/right.jpg */
            right: string;
            /**
             * Format: date-time
             * @description Certificate of Fitness Expiry Date of the truck
             * @example 2025-03-02T20:12:00.000Z
             */
            CertificateOfFitnessExpiry: string;
            /** @example https://example.com/CertificateOfFitness.jpg */
            CertificateOfFitnessFile: string;
            /**
             * @description Truck Insurance Type
             * @example FULL | PARTIAL
             * @enum {string}
             */
            insuranceType?: "FULL" | "PARTIAL";
            /**
             * @description Truck Insurance Company
             * @example Company Name
             */
            insuranceCompany?: string;
            /**
             * @description Truck Insurance Policy Number
             * @example Policy Number
             */
            policyNumber?: string;
            /**
             * @description Truck Insurance Policy File
             * @example https://example.com/policy.jpg
             */
            policyFile?: string;
        };
        InsuranceDto: {
            /**
             * @description ID of the insurance
             * @example 1
             */
            id: string;
            /**
             * @description Type of the insurance
             * @example FULL | THIRD_PARTY
             * @enum {string}
             */
            type: "FULL" | "PARTIAL";
            /**
             * @description Company of the insurance
             * @example XYZ Insurance Provider
             */
            company: string;
            /**
             * @description Policy Number of the insurance
             * @example 1234-333-3333
             */
            policyNumber: string;
            /**
             * Format: date-time
             * @description Creation Date of the insurance
             * @example 2021-09-01T00:00:00.000Z
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Last Update Date of the insurance
             * @example 2021-09-01T00:00:00.000Z
             */
            updatedAt: string;
        };
        TruckDto: {
            /**
             * @description ID of the truck
             * @example 1
             */
            id: string;
            /**
             * @description Capacity of the truck in tons
             * @example 24
             */
            capacity: number;
            /**
             * @description Manufacturer of the truck
             * @example Mercedes
             */
            make: string;
            /**
             * @description Model of the truck
             * @example Actros
             */
            model: string;
            /**
             * @description Registration Number of the truck
             * @example ABC-123
             */
            registration: string;
            /**
             * @description Status of the truck
             * @example ACTIVE | INACTIVE
             * @enum {string}
             */
            status: "ACTIVE" | "INACTIVE";
            /**
             * @description Type of the truck
             * @example STANDARD | SLIDING | TOW
             * @enum {string}
             */
            type: "STANDARD" | "SLIDING" | "TOW";
            /**
             * Format: date-time
             * @description Creation Date of the truck
             * @example 2021-09-01T00:00:00.000Z
             */
            createdAt: string;
            /**
             * @description Insurance ID of the truck
             * @example 1
             */
            insuranceId: string;
            /**
             * Format: date-time
             * @description Registration expiry date of the truck
             * @example 2022-09-01T00:00:00.000Z
             */
            registrationExpiry: string;
            /**
             * Format: date-time
             * @description Last update date of the truck
             * @example 2021-09-01T00:00:00.000Z
             */
            updatedAt: string;
            /**
             * @description Files uploaded for the truck
             * @example [
             *       "https://example.com/file1.jpg",
             *       "https://example.com/file2.jpg",
             *       "https://example.com/file3.jpg",
             *       "https://example.com/file4.jpg"
             *     ]
             */
            files: components["schemas"]["File"][];
            /** @description Insurance of the truck */
            insurance: components["schemas"]["InsuranceDto"];
        };
        TruckAppDto: {
            /**
             * @description Capacity of the truck in tons
             * @example 24
             */
            capacity?: number;
            /**
             * @description Manufacturer of the truck
             * @example Mercedes
             */
            make?: string;
            /**
             * @description Model of the truck
             * @example Actros
             */
            model?: string;
            /**
             * @description Registration Number of the truck
             * @example ABC-123
             */
            registration?: string;
            /**
             * Format: date-time
             * @description Registration Expiry Date of the truck
             * @example 2025-03-02T20:12:00.000Z
             */
            registrationExpiry?: string;
            /**
             * @description Status of the truck
             * @example ACTIVE | INACTIVE
             * @enum {string}
             */
            status?: "ACTIVE" | "INACTIVE";
            /**
             * @description Type of the truck
             * @example STANDARD | SLIDING | TOW
             * @enum {string}
             */
            type?: "STANDARD" | "SLIDING" | "TOW";
            /** @example https://example.com/front.jpg */
            front?: string;
            /** @example https://example.com/back.jpg */
            back?: string;
            /** @example https://example.com/left.jpg */
            left?: string;
            /** @example https://example.com/right.jpg */
            right?: string;
            /**
             * Format: date-time
             * @description Certificate of Fitness Expiry Date of the truck
             * @example 2025-03-02T20:12:00.000Z
             */
            CertificateOfFitnessExpiry?: string;
            /** @example https://example.com/CertificateOfFitness.jpg */
            CertificateOfFitnessFile?: string;
            /**
             * @description Truck Insurance Type
             * @example FULL | PARTIAL
             * @enum {string}
             */
            insuranceType?: "FULL" | "PARTIAL";
            /**
             * @description Truck Insurance Company
             * @example Company Name
             */
            insuranceCompany?: string;
            /**
             * @description Truck Insurance Policy Number
             * @example Policy Number
             */
            policyNumber?: string;
            /**
             * @description Truck Insurance Policy File
             * @example https://example.com/policy.jpg
             */
            policyFile?: string;
            /**
             * @description Truck Insurance Type
             * @example FULL | PARTIAL
             * @enum {string}
             */
            InsuranceType: "FULL" | "PARTIAL";
            /**
             * @description Truck Insurance Company
             * @example Company Name
             */
            InsuranceCompany: string;
            /**
             * @description Truck Insurance Policy Number
             * @example Policy Number
             */
            PolicyNumber: string;
            /**
             * @description Truck Insurance Policy File
             * @example Policy File
             */
            PolicyFile: string;
            /**
             * @description Driver ID
             * @example Driver ID
             */
            DriverId: string;
        };
        UpdateTruckDto: {
            /**
             * @description Capacity of the truck in tons
             * @example 24
             */
            capacity?: number;
            /**
             * @description Manufacturer of the truck
             * @example Mercedes
             */
            make?: string;
            /**
             * @description Model of the truck
             * @example Actros
             */
            model?: string;
            /**
             * @description Registration Number of the truck
             * @example ABC-123
             */
            registration?: string;
            /**
             * Format: date-time
             * @description Registration Expiry Date of the truck
             * @example 2025-03-02T20:12:00.000Z
             */
            registrationExpiry?: string;
            /**
             * @description Status of the truck
             * @example ACTIVE | INACTIVE
             * @enum {string}
             */
            status?: "ACTIVE" | "INACTIVE";
            /**
             * @description Type of the truck
             * @example STANDARD | SLIDING | TOW
             * @enum {string}
             */
            type?: "STANDARD" | "SLIDING" | "TOW";
            /** @example https://example.com/front.jpg */
            front?: string;
            /** @example https://example.com/back.jpg */
            back?: string;
            /** @example https://example.com/left.jpg */
            left?: string;
            /** @example https://example.com/right.jpg */
            right?: string;
            /**
             * Format: date-time
             * @description Certificate of Fitness Expiry Date of the truck
             * @example 2025-03-02T20:12:00.000Z
             */
            CertificateOfFitnessExpiry?: string;
            /** @example https://example.com/CertificateOfFitness.jpg */
            CertificateOfFitnessFile?: string;
            /**
             * @description Truck Insurance Type
             * @example FULL | PARTIAL
             * @enum {string}
             */
            insuranceType?: "FULL" | "PARTIAL";
            /**
             * @description Truck Insurance Company
             * @example Company Name
             */
            insuranceCompany?: string;
            /**
             * @description Truck Insurance Policy Number
             * @example Policy Number
             */
            policyNumber?: string;
            /**
             * @description Truck Insurance Policy File
             * @example https://example.com/policy.jpg
             */
            policyFile?: string;
        };
        CreateClientCarDto: {
            /**
             * @description Model of the car
             * @example Model S
             */
            model: string;
            /**
             * @description Registration number of the car
             * @example XYZ-123
             */
            registration: string;
            /**
             * @description Type of vehicle
             * @example CAR | TRUCK
             * @enum {string}
             */
            vehicleType: "CAR" | "TRUCK";
            /**
             * @description Manufacture year of the vehicle
             * @example 2020
             */
            year: number;
            /**
             * @description Suspension type, if any
             * @example Air Matic Suspension
             */
            suspension?: string;
            /**
             * @description Description of any damage to the car
             * @example Small dent on the rear bumper
             */
            damageDescription: string;
            /** @example https://example.com/front.jpg */
            front: string;
            /** @example https://example.com/back.jpg */
            back: string;
            /** @example https://example.com/left.jpg */
            left: string;
            /** @example https://example.com/right.jpg */
            right: string;
            /** @example https://example.com/insurance.jpg */
            insuranceFile?: string;
            /** @example Company Name */
            insuranceCompany?: string;
            /** @example 1234567890 */
            insurancePolicyNumber?: string;
            /** @example false */
            isMoveable?: boolean;
        };
        ClientCarDto: {
            /**
             * @description ID of the car
             * @example 1a2b3c4d
             */
            id: string;
            /**
             * @description ID of the associated trip
             * @example 5e8f8c8f-8c8f-8c8f-8c8f-8c8f8c8f8c8f
             */
            tripId?: string;
            /**
             * @description Model of the car
             * @example Model S
             */
            model: string;
            /**
             * @description Registration number of the car
             * @example XYZ-123
             */
            registration: string;
            /**
             * @description Type of vehicle
             * @example CAR | TRUCK
             * @enum {string}
             */
            vehicleType: "CAR" | "TRUCK";
            /**
             * @description Manufacture year of the vehicle
             * @example 2020
             */
            year: number;
            /**
             * @description Suspension type, if any
             * @example Air Matic Suspension
             */
            suspension: string;
            /**
             * @description Description of any damage to the car
             * @example Small dent on the rear bumper
             */
            damageDescription: string;
            /**
             * Format: date-time
             * @description Creation date of the record
             * @example 2023-09-01T00:00:00.000Z
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Last update date of the record
             * @example 2023-09-01T00:00:00.000Z
             */
            updatedAt: string;
            /** @description Files associated with the car */
            files: components["schemas"]["File"][];
        };
        UpdateClientCarDto: {
            /**
             * @description Model of the car
             * @example Model S
             */
            model?: string;
            /**
             * @description Registration number of the car
             * @example XYZ-123
             */
            registration?: string;
            /**
             * @description Type of vehicle
             * @example CAR | TRUCK
             * @enum {string}
             */
            vehicleType?: "CAR" | "TRUCK";
            /**
             * @description Manufacture year of the vehicle
             * @example 2020
             */
            year?: number;
            /**
             * @description Suspension type, if any
             * @example Air Matic Suspension
             */
            suspension?: string;
            /**
             * @description Description of any damage to the car
             * @example Small dent on the rear bumper
             */
            damageDescription?: string;
            /** @example https://example.com/front.jpg */
            front?: string;
            /** @example https://example.com/back.jpg */
            back?: string;
            /** @example https://example.com/left.jpg */
            left?: string;
            /** @example https://example.com/right.jpg */
            right?: string;
            /** @example https://example.com/insurance.jpg */
            insuranceFile?: string;
            /** @example Company Name */
            insuranceCompany?: string;
            /** @example 1234567890 */
            insurancePolicyNumber?: string;
            /** @example false */
            isMoveable?: boolean;
        };
        DashboardStatsDto: {
            /** @description Total number of registered drivers */
            totalDrivers: number;
            /** @description Total number of completed trips */
            completedTrips: number;
            /** @description Total number of clients */
            totalClients: number;
            /** @description Total Revenue */
            revenue: number;
        };
        CustomerDto: {
            /**
             * @description Unique identifier of the customer
             * @example 12121-12121-12121-12121
             */
            id: string;
            /**
             * @description User id provided by google identity provider
             * @example i59pTszPnTY12nF1ErZCU9bbJV52
             */
            userId: string;
            /** @description Customer  */
            user: components["schemas"]["UserDto"];
            /**
             * Format: date-time
             * @description Created At
             * @example 2021-10-10T10:10:10.000Z
             */
            createdAt: string;
            /**
             * Format: date-time
             * @description Updated At
             * @example 2021-10-10T10:10:10.000Z
             */
            updatedAt: string;
        };
    };
    responses: never;
    parameters: never;
    requestBodies: never;
    headers: never;
    pathItems: never;
}
export type $defs = Record<string, never>;
export interface operations {
    AppController_getHello: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    AppController_test: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    LocationsController_findAll: {
        parameters: {
            query?: {
                order?: "ASC" | "DESC";
                page?: number;
                take?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description List of all the locations */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PageDto"] & {
                        data?: components["schemas"]["LocationDto"][];
                    };
                };
            };
        };
    };
    LocationsController_create: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CreateLocationDto"];
            };
        };
        responses: {
            /** @description Created location record */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["LocationDto"];
                };
            };
        };
    };
    LocationsController_getClosestLocation: {
        parameters: {
            query: {
                /** @description Source location for the search (format: latitude,longitude) */
                From: string;
                /** @description Type of the location */
                type: "YARD" | "MECHANICSHOP" | "ILLEGAL_PARKING";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Getting closest location BY TYPE : YARD | MECHANICSHOP | ILLEGAL_PARKING */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["LocationDto"];
                };
            };
        };
    };
    LocationsController_findOne: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The location record by id */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["LocationDto"];
                };
            };
        };
    };
    LocationsController_remove: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Deleted location record */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["LocationDto"];
                };
            };
        };
    };
    LocationsController_update: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdateLocationDto"];
            };
        };
        responses: {
            /** @description Updated location record */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["LocationDto"];
                };
            };
        };
    };
    UsersController_findAll: {
        parameters: {
            query?: {
                order?: "ASC" | "DESC";
                page?: number;
                take?: number;
                role?: "CUSTOMER" | "DRIVER" | "ADMIN" | "MANAGER" | "STAFF" | "POLICE";
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description List of all the users */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PageDto"] & {
                        data?: components["schemas"]["UserDto"][];
                    };
                };
            };
        };
    };
    UsersController_create: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["RegisterUserDto"];
            };
        };
        responses: {
            /** @description Registered user */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserDto"];
                };
            };
        };
    };
    UsersController_findCurrentUser: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The current user */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserDto"];
                };
            };
        };
    };
    UsersController_updateCurrentUser: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdateCurrentUserDto"];
            };
        };
        responses: {
            /** @description Updating current user */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserDto"];
                };
            };
        };
    };
    UsersController_findOne: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The user record by ID */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserDto"];
                };
            };
        };
    };
    UsersController_remove: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Deleted user record */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserDto"];
                };
            };
        };
    };
    UsersController_update: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdateUserRoleDto"];
            };
        };
        responses: {
            /** @description Updated user record */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UpdateUserDto"];
                };
            };
        };
    };
    UsersController_addCard: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": string;
            };
        };
        responses: {
            /** @description Add card to user */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserDto"];
                };
            };
        };
    };
    UsersController_listCards: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description List of cards */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserDto"];
                };
            };
        };
    };
    UsersController_removeCard: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                cardId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Remove card */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UserDto"];
                };
            };
        };
    };
    UsersController_verifyEmail: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["EmailDto"];
            };
        };
        responses: {
            /** @description Email verification status */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["EmailDto"];
                };
            };
        };
    };
    InvoicesController_findAll: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description list all invoices */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PageDto"] & {
                        data?: components["schemas"]["CreateInvoiceDto"][];
                    };
                };
            };
        };
    };
    InvoicesController_create: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CreateInvoiceDto"];
            };
        };
        responses: {
            /** @description Invoice created successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CreateInvoiceDto"];
                };
            };
        };
    };
    InvoicesController_findOne: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Invoice found successfully by ID */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CreateInvoiceDto"];
                };
            };
        };
    };
    InvoicesController_remove: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Invoice deleted successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CreateInvoiceDto"];
                };
            };
        };
    };
    InvoicesController_update: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdateInvoiceDto"];
            };
        };
        responses: {
            /** @description Invoice updated successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CreateInvoiceDto"];
                };
            };
        };
    };
    PaymentController_createPaymentIntent: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Invoice ID to create payment intent for */
        requestBody: {
            content: {
                "application/json": components["schemas"]["CreatePaymentIntentDto"];
            };
        };
        responses: {
            /** @description Payment intent created successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example pi_3NdF0S2eZvKYlo2C1KHgxErZ */
                        id?: string;
                        /** @example payment_intent */
                        object?: string;
                        /** @example 2000 */
                        amount?: number;
                        /** @example usd */
                        currency?: string;
                        /** @example requires_payment_method */
                        status?: string;
                        /** @example pi_3NdF0S2eZvKYlo2C1KHgxErZ_secret_YTVfkEXGlpRZJ1jWEQh9n9Xla */
                        client_secret?: string;
                    };
                };
            };
            /** @description Invoice not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    PaymentController_preAuthorizeTripPayment: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Pre-authorization details */
        requestBody: {
            content: {
                "application/json": components["schemas"]["PreAuthorizeTripPaymentDto"];
            };
        };
        responses: {
            /** @description Payment pre-authorized successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example pi_3NdF0S2eZvKYlo2C1KHgxErZ */
                        id?: string;
                        /** @example payment_intent */
                        object?: string;
                        /** @example 2000 */
                        amount?: number;
                        /** @example usd */
                        currency?: string;
                        /** @example requires_capture */
                        status?: string;
                        /** @example manual */
                        capture_method?: string;
                    };
                };
            };
            /** @description No payment methods found for customer or authorization failed */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    PaymentController_captureTripPayment: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Capture payment details */
        requestBody: {
            content: {
                "application/json": components["schemas"]["CaptureTripPaymentDto"];
            };
        };
        responses: {
            /** @description Payment captured successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example pi_3NdF0S2eZvKYlo2C1KHgxErZ */
                        id?: string;
                        /** @example payment_intent */
                        object?: string;
                        /** @example 2000 */
                        amount?: number;
                        /** @example 2000 */
                        amount_received?: number;
                        /** @example usd */
                        currency?: string;
                        /** @example succeeded */
                        status?: string;
                    };
                };
            };
            /** @description Payment intent is in invalid state or capture failed */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    PaymentController_cancelTripPreAuthorization: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Cancel payment details */
        requestBody: {
            content: {
                "application/json": components["schemas"]["CancelPaymentDto"];
            };
        };
        responses: {
            /** @description Payment pre-authorization cancelled successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example pi_3NdF0S2eZvKYlo2C1KHgxErZ */
                        id?: string;
                        /** @example payment_intent */
                        object?: string;
                        /** @example canceled */
                        status?: string;
                        /** @example requested_by_customer */
                        cancellation_reason?: string;
                    };
                };
            };
            /** @description Failed to cancel pre-authorization */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    PaymentController_createStripeCustomer: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Customer creation details */
        requestBody: {
            content: {
                "application/json": components["schemas"]["CreateStripeCustomerDto"];
            };
        };
        responses: {
            /** @description Stripe customer created successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example cus_NdF0S2eZvKYlo2C */
                        id?: string;
                        /** @example customer */
                        object?: string;
                        /** @example John Doe */
                        name?: string;
                        /** @example <EMAIL> */
                        email?: string;
                        /** @example 1627383573 */
                        created?: number;
                    };
                };
            };
        };
    };
    PaymentController_updateStripeCustomer: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Stripe customer ID */
                customerId: string;
            };
            cookie?: never;
        };
        /** @description Customer update details */
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdateStripeCustomerDto"];
            };
        };
        responses: {
            /** @description Stripe customer updated successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example cus_NdF0S2eZvKYlo2C */
                        id?: string;
                        /** @example customer */
                        object?: string;
                        /** @example John Updated */
                        name?: string;
                        /** @example <EMAIL> */
                        email?: string;
                    };
                };
            };
        };
    };
    PaymentController_listCardsForStripeCustomer: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Stripe customer ID */
                customerId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Cards for Stripe customer retrieved successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example list */
                        object?: string;
                        data?: {
                            /** @example pm_1NdFPd2eZvKYlo2C1lgTCDTp */
                            id?: string;
                            /** @example payment_method */
                            object?: string;
                            /** @example card */
                            type?: string;
                            card?: {
                                /** @example visa */
                                brand?: string;
                                /** @example 4242 */
                                last4?: string;
                                /** @example 12 */
                                exp_month?: number;
                                /** @example 2024 */
                                exp_year?: number;
                            };
                        }[];
                        /** @example false */
                        has_more?: boolean;
                    };
                };
            };
        };
    };
    PaymentController_attachCardsWithStripeCustomer: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Stripe customer ID */
                customerId: string;
            };
            cookie?: never;
        };
        /** @description Payment method attachment details */
        requestBody: {
            content: {
                "application/json": components["schemas"]["AttachCardDto"];
            };
        };
        responses: {
            /** @description Card attached to Stripe customer successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example seti_1NdFRP2eZvKYlo2C1blPIdVj */
                        id?: string;
                        /** @example setup_intent */
                        object?: string;
                        /** @example succeeded */
                        status?: string;
                        /** @example pm_1NdFPd2eZvKYlo2C1lgTCDTp */
                        payment_method?: string;
                        /** @example cus_NdF0S2eZvKYlo2C */
                        customer?: string;
                    };
                };
            };
        };
    };
    PaymentController_deleteCardForStripeCustomer: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                /** @description Stripe customer ID */
                customerId: string;
            };
            cookie?: never;
        };
        /** @description Card deletion details */
        requestBody: {
            content: {
                "application/json": components["schemas"]["DeleteCardDto"];
            };
        };
        responses: {
            /** @description Card deleted from Stripe customer successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example card_1NdFPd2eZvKYlo2C1lgT */
                        id?: string;
                        /** @example card */
                        object?: string;
                        /** @example true */
                        deleted?: boolean;
                    };
                };
            };
        };
    };
    PaymentController_createCustomerPortalSession: {
        parameters: {
            query: {
                /** @description Stripe customer ID */
                customerId: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Customer portal session details */
        requestBody: {
            content: {
                "application/json": components["schemas"]["CustomerPortalSessionDto"];
            };
        };
        responses: {
            /** @description Customer portal session created successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example cs_test_a1bcdefghijklmnopqrstuvwxyz */
                        id?: string;
                        /** @example billing_portal.session */
                        object?: string;
                        /** @example https://billing.stripe.com/session/live_test_a1bcdefg */
                        url?: string;
                        /** @example https://example.com/account */
                        return_url?: string;
                    };
                };
            };
        };
    };
    PaymentController_listCustomerPortalConfigurations: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Customer portal configurations retrieved successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example list */
                        object?: string;
                        data?: {
                            /** @example bpc_1NdFPd2eZvKYlo2C1lgT */
                            id?: string;
                            /** @example billing_portal.configuration */
                            object?: string;
                            /** @example true */
                            is_default?: boolean;
                            business_profile?: {
                                /** @example ACME Inc. Customer Portal */
                                headline?: string;
                                /** @example https://example.com/privacy */
                                privacy_policy_url?: string;
                                /** @example https://example.com/terms */
                                terms_of_service_url?: string;
                            };
                        }[];
                        /** @example false */
                        has_more?: boolean;
                    };
                };
            };
        };
    };
    PaymentController_createCustomerPortalConfiguration: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Portal configuration details */
        requestBody: {
            content: {
                "application/json": components["schemas"]["CustomerPortalConfigurationDto"];
            };
        };
        responses: {
            /** @description Customer portal configuration created successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example bpc_1NdFPd2eZvKYlo2C1lgT */
                        id?: string;
                        /** @example billing_portal.configuration */
                        object?: string;
                        /** @example true */
                        is_default?: boolean;
                        business_profile?: {
                            /** @example ACME Inc. Customer Portal */
                            headline?: string;
                            /** @example https://example.com/privacy */
                            privacy_policy_url?: string;
                            /** @example https://example.com/terms */
                            terms_of_service_url?: string;
                        };
                    };
                };
            };
        };
    };
    PaymentController_updateCustomerPortalConfiguration: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        /** @description Portal configuration update details */
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdatePortalConfigurationDto"];
            };
        };
        responses: {
            /** @description Customer portal configuration updated successfully */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": {
                        /** @example bpc_1NdFPd2eZvKYlo2C1lgT */
                        id?: string;
                        /** @example billing_portal.configuration */
                        object?: string;
                        /** @example 1627383573 */
                        updated?: number;
                        business_profile?: {
                            /** @example Updated Customer Portal */
                            headline?: string;
                            /** @example https://example.com/privacy */
                            privacy_policy_url?: string;
                            /** @example https://example.com/terms */
                            terms_of_service_url?: string;
                        };
                    };
                };
            };
        };
    };
    StripeWebhookController_handleWebhook: {
        parameters: {
            query?: never;
            header: {
                "stripe-signature": string;
            };
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    RidesController_preRideCostCalculation: {
        parameters: {
            query?: never;
            header?: {
                /** @description Special header to trigger a fake response */
                "x-fake"?: string;
            };
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TripCostRequestDto"];
            };
        };
        responses: {
            /** @description Getting the cost of the ride. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RideCalculationDto"];
                };
            };
        };
    };
    RidesController_getRideCalculationById: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                calculationId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Getting the cost of the ride by ID. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RideCalculationDto"];
                };
            };
        };
    };
    RidesController_request: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["RequestRideDto"];
            };
        };
        responses: {
            /** @description Requesting a ride. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    RidesController_acceptRide: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                rideId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Accept a Ride Request. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    RidesController_getRideDetails: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                rideId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Get Ride Details by RideId */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    RidesController_completeRide: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CompleteRideDto"];
            };
        };
        responses: {
            /** @description Completing a ride. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CompleteRideDto"];
                };
            };
        };
    };
    RidesController_chargeCustomer: {
        parameters: {
            query: {
                tripId: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Charging the customer. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CompleteRideDto"];
                };
            };
        };
    };
    SettingsController_findAll: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Getting All Trip Type Fares */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PageDto"] & {
                        data?: components["schemas"]["SettingsDto"][];
                    };
                };
            };
        };
    };
    SettingsController_create: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CreateSettingDto"];
            };
        };
        responses: {
            /** @description Creating Fare for Trip Types */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SettingsDto"];
                };
            };
        };
    };
    SettingsController_findAllTypes: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Trip Types retrieved successfully. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": Record<string, never>;
                };
            };
        };
    };
    SettingsController_findOne: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Getting Trip type Fare by ID. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SettingsDto"];
                };
            };
        };
    };
    SettingsController_remove: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Deleting Trip Type Fare by ID. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["SettingsDto"];
                };
            };
        };
    };
    SettingsController_findByType: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                type: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Getting Trip Type Fare by TYPE. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TripSettingsDto"];
                };
            };
        };
    };
    SettingsController_update: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                type: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdatePriceDto"];
            };
        };
        responses: {
            /** @description Updating Trip Type Fare by TYPE. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TripSettingsDto"];
                };
            };
        };
    };
    TripsController_findAll: {
        parameters: {
            query?: {
                order?: "ASC" | "DESC";
                page?: number;
                take?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description List of all the trips */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PageDto"] & {
                        data?: components["schemas"]["TripDto"][];
                    };
                };
            };
        };
    };
    TripsController_getTotalTrips: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Total number of Completed Trips */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": number;
                };
            };
        };
    };
    TripsController_getRecentTrips: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description 5 most recent trips with driver information */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["RecentTripDto"][];
                };
            };
        };
    };
    TripsController_findOne: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Getting trip By ID. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TripDto"];
                };
            };
        };
    };
    TripsController_remove: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Trip Removed . */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TripDto"];
                };
            };
        };
    };
    TripsController_update: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdateTripDto"];
            };
        };
        responses: {
            /** @description Updating Trip By ID. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TripDto"];
                };
            };
        };
    };
    TripsController_findTripsByCustomerId: {
        parameters: {
            query?: {
                order?: "ASC" | "DESC";
                page?: number;
                take?: number;
            };
            header?: never;
            path: {
                customerId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description List of all the trips of a customer */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PageDto"] & {
                        data?: components["schemas"]["TripDto"][];
                    };
                };
            };
        };
    };
    TripsController_findTripsByDriverId: {
        parameters: {
            query?: {
                order?: "ASC" | "DESC";
                page?: number;
                take?: number;
            };
            header?: never;
            path: {
                driverId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description List of all the trips of a driver */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PageDto"] & {
                        data?: components["schemas"]["TripDto"][];
                    };
                };
            };
        };
    };
    TripsController_rateTrip: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CreateRatingDto"];
            };
        };
        responses: {
            /** @description Rating created successfully */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Bad request */
            400: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
            /** @description Trip not found */
            404: {
                headers: {
                    [name: string]: unknown;
                };
                content?: never;
            };
        };
    };
    DriversController_register: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["RegisterDriverDto"];
            };
        };
        responses: {
            /** @description Registered driver */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DriverDto"];
                };
            };
        };
    };
    DriversController_registerAdmin: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["RegisterDriverAdminDto"];
            };
        };
        responses: {
            /** @description Registered driver */
            201: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DriverDto"];
                };
            };
        };
    };
    DriversController_findAll: {
        parameters: {
            query?: {
                order?: "ASC" | "DESC";
                page?: number;
                take?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description List of all the drivers */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PageDto"] & {
                        data?: components["schemas"]["DriverDto"][];
                    };
                };
            };
        };
    };
    DriversController_findCurrentDriver: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The current driver */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DriverDto"];
                };
            };
        };
    };
    DriversController_total: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Total number of drivers */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": number;
                };
            };
        };
    };
    DriversController_findClosesDriver: {
        parameters: {
            query: {
                lat: number;
                lng: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The closest driver */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DriverDto"];
                };
            };
        };
    };
    DriversController_findOne: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The driver record */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DriverDto"];
                };
            };
        };
    };
    DriversController_remove: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Driver deleted */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DriverDto"];
                };
            };
        };
    };
    DriversController_update: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdateDriverDto"];
            };
        };
        responses: {
            /** @description Updated driver */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["UpdateDriverDto"];
                };
            };
        };
    };
    DriversController_findByStatus: {
        parameters: {
            query?: {
                order?: "ASC" | "DESC";
                page?: number;
                take?: number;
            };
            header?: never;
            path: {
                statusType: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description List of all the drivers by status */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PageDto"] & {
                        data?: components["schemas"]["DriverDto"][];
                    };
                };
            };
        };
    };
    DriversController_updateWorkStatus: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdateDriverStatusDto"];
            };
        };
        responses: {
            /** @description Updates driver status */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DriverDto"];
                };
            };
        };
    };
    TrucksController_findAll: {
        parameters: {
            query?: {
                order?: "ASC" | "DESC";
                page?: number;
                take?: number;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description List of all the trucks */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PageDto"] & {
                        data?: components["schemas"]["TruckDto"][];
                    };
                };
            };
        };
    };
    TrucksController_create: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CreateTruckDto"];
            };
        };
        responses: {
            /** @description Creating a Truck. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TruckDto"];
                };
            };
        };
    };
    TrucksController_createAppTruck: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["TruckAppDto"];
            };
        };
        responses: {
            /** @description Truck Creation For Driver App . */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TruckAppDto"];
                };
            };
        };
    };
    TrucksController_findOne: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Get Truck by ID. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TruckDto"];
                };
            };
        };
    };
    TrucksController_remove: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Deleting Truck by ID. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TruckDto"];
                };
            };
        };
    };
    TrucksController_update: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdateTruckDto"];
            };
        };
        responses: {
            /** @description Updating Truck by ID. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["TruckDto"];
                };
            };
        };
    };
    ClientCarController_findAll: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description List of all the clientcars */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["PageDto"] & {
                        data?: components["schemas"]["ClientCarDto"][];
                    };
                };
            };
        };
    };
    ClientCarController_create: {
        parameters: {
            query: {
                isInsured: boolean;
                tripId: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["CreateClientCarDto"];
            };
        };
        responses: {
            /** @description Client Car Creation with optional trip association. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ClientCarDto"];
                };
            };
        };
    };
    ClientCarController_findByTripId: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                tripId: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Getting Client Car by Trip ID. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ClientCarDto"][];
                };
            };
        };
    };
    ClientCarController_findOne: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Getting Client Car by ID. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ClientCarDto"];
                };
            };
        };
    };
    ClientCarController_remove: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Deleting client Car by ID. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ClientCarDto"];
                };
            };
        };
    };
    ClientCarController_update: {
        parameters: {
            query?: never;
            header?: never;
            path: {
                id: string;
            };
            cookie?: never;
        };
        requestBody: {
            content: {
                "application/json": components["schemas"]["UpdateClientCarDto"];
            };
        };
        responses: {
            /** @description Updating client Car by ID. */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["ClientCarDto"];
                };
            };
        };
    };
    DashboardController_getTotal: {
        parameters: {
            query: {
                /** @description Start date for the revenue query (format: yyyy-mm-dd) */
                startDate: string;
                /** @description End date for the revenue query (format: yyyy-mm-dd) */
                endDate: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Returns the total number of Registered Drivers, Completed trips, and clients */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["DashboardStatsDto"];
                };
            };
        };
    };
    DashboardController_getTermsAndConditions: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Returns the terms and conditions */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": string;
                };
            };
        };
    };
    DashboardController_getRevenueStatistics: {
        parameters: {
            query: {
                /** @description Start date for the revenue query (format: yyyy-mm-dd) */
                startDate: string;
                /** @description End date for the revenue query (format: yyyy-mm-dd) */
                endDate: string;
            };
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description Returns the revenue statistics */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": Record<string, never>;
                };
            };
        };
    };
    CustomerController_findCurrentUser: {
        parameters: {
            query?: never;
            header?: never;
            path?: never;
            cookie?: never;
        };
        requestBody?: never;
        responses: {
            /** @description The current Customer */
            200: {
                headers: {
                    [name: string]: unknown;
                };
                content: {
                    "application/json": components["schemas"]["CustomerDto"];
                };
            };
        };
    };
}
