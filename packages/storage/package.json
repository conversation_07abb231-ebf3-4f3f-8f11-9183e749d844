{"name": "@x/storage", "version": "1.0.0", "description": "Firebase Storage utilities for React Native and Web", "main": "index.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist"}, "dependencies": {"@react-native-firebase/storage": "^22.2.1"}, "peerDependencies": {"react": "*", "react-native": "*"}, "devDependencies": {"@types/react": "*", "@types/react-native": "*", "typescript": "*"}, "keywords": ["firebase", "storage", "react-native", "expo"], "author": "Towing <PERSON>", "license": "MIT"}