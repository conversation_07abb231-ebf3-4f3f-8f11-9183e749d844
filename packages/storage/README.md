# @x/storage

Firebase Storage utilities for React Native applications in the Towing Guy monorepo.

## Features

- 🔥 Firebase Storage integration for React Native
- 📱 Optimized for mobile file uploads
- 🚗 Specialized car photo upload utilities
- 🔄 Concurrent upload support
- 📊 Upload progress tracking
- 🛡️ Error handling and validation

## Installation

This package is part of the monorepo workspace and is automatically available to other packages.

## Usage

### Basic File Upload

```typescript
import { useStorage } from '@x/storage';

function MyComponent() {
  const { uploadFile } = useStorage();

  const handleUpload = async (fileUri: string) => {
    try {
      const result = await uploadFile(fileUri, 'my-folder');
      console.log('File uploaded:', result.url);
    } catch (error) {
      console.error('Upload failed:', error);
    }
  };
}
```

### Car Photo Upload

```typescript
import { useCarPhotoUpload } from '@x/storage';

function CarDetailsForm() {
  const { uploadCarPhotos, uploadState } = useCarPhotoUpload();

  const handleSubmit = async () => {
    const photos = {
      front: 'file://path/to/front.jpg',
      back: 'file://path/to/back.jpg',
      left: 'file://path/to/left.jpg',
      right: 'file://path/to/right.jpg',
    };

    try {
      const uploadedUrls = await uploadCarPhotos(photos, 'ride-123');
      console.log('Photos uploaded:', uploadedUrls);
    } catch (error) {
      console.error('Upload failed:', error);
    }
  };

  if (uploadState.isUploading) {
    return <Text>Uploading photos...</Text>;
  }

  return (
    <Button onPress={handleSubmit}>
      Upload Car Photos
    </Button>
  );
}
```

## API Reference

### useStorage()

Basic Firebase Storage operations.

#### Methods

- `uploadFile(uri, folder?, fileName?)` - Upload a single file
- `uploadMultipleFiles(files, folder?)` - Upload multiple files concurrently
- `deleteFile(path)` - Delete a file from storage
- `getDownloadURL(path)` - Get download URL for a file

### useCarPhotoUpload()

Specialized hook for car photo uploads with progress tracking.

#### Methods

- `uploadCarPhotos(photos, carId)` - Upload car photos sequentially
- `uploadCarPhotosConcurrent(photos, carId)` - Upload car photos concurrently
- `resetUploadState()` - Reset upload state

#### State

- `uploadState.isUploading` - Whether upload is in progress
- `uploadState.uploadProgress` - Progress for each photo position
- `uploadState.error` - Error message if upload fails

## Storage Structure

Car photos are organized in Firebase Storage as:

```
car-photos/
  ├── {carId}/
  │   ├── front.jpg
  │   ├── back.jpg
  │   ├── left.jpg
  │   └── right.jpg
```

## Dependencies

- `@react-native-firebase/storage` - Firebase Storage for React Native
- `react` - React hooks
- `react-native` - React Native platform
