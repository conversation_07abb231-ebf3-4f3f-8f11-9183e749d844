import { useState } from 'react';
import { useStorage, UploadResult } from './use-storage';

export interface CarPhotoUrls {
  front?: string;
  back?: string;
  left?: string;
  right?: string;
}

export interface CarPhotoUploadState {
  isUploading: boolean;
  uploadProgress: {
    front: boolean;
    back: boolean;
    left: boolean;
    right: boolean;
  };
  error?: string;
}

export function useCarPhotoUpload() {
  const { uploadFile, uploadMultipleFiles } = useStorage();
  
  const [uploadState, setUploadState] = useState<CarPhotoUploadState>({
    isUploading: false,
    uploadProgress: {
      front: false,
      back: false,
      left: false,
      right: false,
    },
  });

  /**
   * Upload car photos to Firebase Storage
   * @param photos - Object containing local URIs for car photos
   * @param carId - Unique identifier for the car (e.g., ride ID or car registration)
   * @returns Promise with uploaded photo URLs
   */
  async function uploadCarPhotos(
    photos: CarPhotoUrls,
    carId: string
  ): Promise<CarPhotoUrls> {
    if (!carId) {
      throw new Error('Car ID is required for photo upload');
    }

    setUploadState(prev => ({
      ...prev,
      isUploading: true,
      error: undefined,
    }));

    try {
      const uploadedUrls: CarPhotoUrls = {};
      const folder = `car-photos/${carId}`;

      // Upload each photo if it exists
      if (photos.front) {
        setUploadState(prev => ({
          ...prev,
          uploadProgress: { ...prev.uploadProgress, front: true },
        }));
        const result = await uploadFile(photos.front, folder, 'front.jpg');
        uploadedUrls.front = result.url;
      }

      if (photos.back) {
        setUploadState(prev => ({
          ...prev,
          uploadProgress: { ...prev.uploadProgress, back: true },
        }));
        const result = await uploadFile(photos.back, folder, 'back.jpg');
        uploadedUrls.back = result.url;
      }

      if (photos.left) {
        setUploadState(prev => ({
          ...prev,
          uploadProgress: { ...prev.uploadProgress, left: true },
        }));
        const result = await uploadFile(photos.left, folder, 'left.jpg');
        uploadedUrls.left = result.url;
      }

      if (photos.right) {
        setUploadState(prev => ({
          ...prev,
          uploadProgress: { ...prev.uploadProgress, right: true },
        }));
        const result = await uploadFile(photos.right, folder, 'right.jpg');
        uploadedUrls.right = result.url;
      }

      setUploadState(prev => ({
        ...prev,
        isUploading: false,
      }));

      return uploadedUrls;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setUploadState(prev => ({
        ...prev,
        isUploading: false,
        error: errorMessage,
      }));
      throw error;
    }
  }

  /**
   * Upload car photos concurrently for better performance
   * @param photos - Object containing local URIs for car photos
   * @param carId - Unique identifier for the car
   * @returns Promise with uploaded photo URLs
   */
  async function uploadCarPhotosConcurrent(
    photos: CarPhotoUrls,
    carId: string
  ): Promise<CarPhotoUrls> {
    if (!carId) {
      throw new Error('Car ID is required for photo upload');
    }

    setUploadState(prev => ({
      ...prev,
      isUploading: true,
      error: undefined,
    }));

    try {
      const folder = `car-photos/${carId}`;
      const filesToUpload: Array<{ uri: string; fileName: string; position: keyof CarPhotoUrls }> = [];

      // Prepare files for upload
      if (photos.front) filesToUpload.push({ uri: photos.front, fileName: 'front.jpg', position: 'front' });
      if (photos.back) filesToUpload.push({ uri: photos.back, fileName: 'back.jpg', position: 'back' });
      if (photos.left) filesToUpload.push({ uri: photos.left, fileName: 'left.jpg', position: 'left' });
      if (photos.right) filesToUpload.push({ uri: photos.right, fileName: 'right.jpg', position: 'right' });

      if (filesToUpload.length === 0) {
        setUploadState(prev => ({ ...prev, isUploading: false }));
        return {};
      }

      // Upload all files concurrently
      const uploadResults = await uploadMultipleFiles(
        filesToUpload.map(file => ({ uri: file.uri, fileName: file.fileName })),
        folder
      );

      // Map results back to photo positions
      const uploadedUrls: CarPhotoUrls = {};
      uploadResults.forEach((result, index) => {
        const position = filesToUpload[index].position;
        uploadedUrls[position] = result.url;
      });

      setUploadState(prev => ({
        ...prev,
        isUploading: false,
      }));

      return uploadedUrls;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      setUploadState(prev => ({
        ...prev,
        isUploading: false,
        error: errorMessage,
      }));
      throw error;
    }
  }

  /**
   * Reset upload state
   */
  function resetUploadState() {
    setUploadState({
      isUploading: false,
      uploadProgress: {
        front: false,
        back: false,
        left: false,
        right: false,
      },
      error: undefined,
    });
  }

  return {
    uploadCarPhotos,
    uploadCarPhotosConcurrent,
    uploadState,
    resetUploadState,
  };
}
