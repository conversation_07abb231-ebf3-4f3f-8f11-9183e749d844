import storage from '@react-native-firebase/storage';

export interface UploadResult {
  url: string;
  path: string;
}

export function useStorage() {
  /**
   * Upload a file to Firebase Storage
   * @param uri - Local file URI from image picker or camera
   * @param folder - Storage folder path (e.g., 'car-photos', 'profile-pics')
   * @param fileName - Optional custom filename, will generate one if not provided
   * @returns Promise with download URL and storage path
   */
  async function uploadFile(
    uri: string,
    folder: string = 'uploads',
    fileName?: string
  ): Promise<UploadResult> {
    if (!uri) {
      throw new Error('File URI is required');
    }

    try {
      // Generate unique filename if not provided
      const timestamp = Date.now();
      const randomId = Math.random().toString(36).substring(7);
      const fileExtension = uri.split('.').pop() || 'jpg';
      const finalFileName = fileName || `${timestamp}-${randomId}.${fileExtension}`;
      
      // Create storage path
      const storagePath = `${folder}/${finalFileName}`;
      
      // Create reference to Firebase Storage
      const reference = storage().ref(storagePath);
      
      // Upload file
      await reference.putFile(uri);
      
      // Get download URL
      const downloadURL = await reference.getDownloadURL();
      
      return {
        url: downloadURL,
        path: storagePath
      };
    } catch (error) {
      console.error('Error uploading file:', error);
      throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Upload multiple files concurrently
   * @param files - Array of objects with uri and optional fileName
   * @param folder - Storage folder path
   * @returns Promise with array of upload results
   */
  async function uploadMultipleFiles(
    files: Array<{ uri: string; fileName?: string }>,
    folder: string = 'uploads'
  ): Promise<UploadResult[]> {
    if (!files || files.length === 0) {
      return [];
    }

    try {
      const uploadPromises = files.map(file => 
        uploadFile(file.uri, folder, file.fileName)
      );
      
      return await Promise.all(uploadPromises);
    } catch (error) {
      console.error('Error uploading multiple files:', error);
      throw new Error(`Failed to upload files: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Delete a file from Firebase Storage
   * @param path - Storage path of the file to delete
   */
  async function deleteFile(path: string): Promise<void> {
    if (!path) {
      throw new Error('File path is required');
    }

    try {
      const reference = storage().ref(path);
      await reference.delete();
    } catch (error) {
      console.error('Error deleting file:', error);
      throw new Error(`Failed to delete file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get download URL for a file
   * @param path - Storage path of the file
   * @returns Download URL
   */
  async function getDownloadURL(path: string): Promise<string> {
    if (!path) {
      throw new Error('File path is required');
    }

    try {
      const reference = storage().ref(path);
      return await reference.getDownloadURL();
    } catch (error) {
      console.error('Error getting download URL:', error);
      throw new Error(`Failed to get download URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  return {
    uploadFile,
    uploadMultipleFiles,
    deleteFile,
    getDownloadURL,
  };
}
