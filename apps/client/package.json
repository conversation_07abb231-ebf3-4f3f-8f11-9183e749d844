{"name": "client", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "run:android": "expo run:android", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@hookform/resolvers": "^5.0.1", "@react-native-firebase/app": "^22.2.0", "@react-native-firebase/auth": "^22.2.0", "@react-native-google-signin/google-signin": "^14.0.1", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/native": "^7.1.9", "@tanstack/react-query": "^5.77.0", "@x/api": "workspace:*", "@x/auth": "workspace:*", "@x/rn-shared": "workspace:*", "expo": "^53.0.9", "expo-blur": "~14.1.4", "expo-constants": "~17.1.6", "expo-dev-client": "^5.1.8", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image-picker": "^16.1.4", "expo-linking": "~7.1.5", "expo-location": "^18.1.5", "expo-router": "~5.0.7", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-native": "0.79.2", "react-native-gesture-handler": "~2.25.0", "react-native-maps": "1.23.8", "react-native-maps-directions": "^1.9.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "5.4.1", "react-native-screens": "~4.10.0", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "socket.io-client": "^4.8.1", "zod": "^3.25.28"}, "devDependencies": {"@babel/core": "^7.27.1", "@types/jest": "^29.5.14", "@types/react": "~19.1.5", "@types/react-test-renderer": "^19.1.0", "jest": "^29.7.0", "jest-expo": "~53.0.5", "react-test-renderer": "19.1.0", "typescript": "^5.8.3"}, "private": true}