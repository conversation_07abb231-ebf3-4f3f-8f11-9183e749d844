import { useLocalSearchParams } from "expo-router";
import { Text } from "react-native-paper";
import { XView } from "@x/rn-shared";

export default function TripSummaryPage() {
  const { tripId } = useLocalSearchParams<{ tripId: string }>();

  // TODO: call the endpoint to get the details of the trip and show the summary.

  return (
    <XView>
      <Text>Trip Id: {tripId}</Text>
      <Text>Showing summary of the trip</Text>
    </XView>
  );
}
