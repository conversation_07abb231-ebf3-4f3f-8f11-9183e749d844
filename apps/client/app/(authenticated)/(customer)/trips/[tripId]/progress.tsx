import { Snackbar, Text } from "react-native-paper";
import { useLocalSearchParams, useRouter } from "expo-router";
import useRideSocket from "@/features/request-ride/hooks/use-ride-socket";
import MapView, { Marker } from "react-native-maps";
import { Image } from "react-native";
import { parseCoords, useCurrentLocation, XView } from "@x/rn-shared";
import { useEffect, useRef, useState } from "react";
import { useSuspenseQuery } from "@tanstack/react-query";
import { queries } from "@x/api";

export default function TripProgressPage() {
  const { tripId } = useLocalSearchParams<{ tripId: string }>();
  const { data: currentLocation } = useCurrentLocation();
  const router = useRouter();
  const [promptMessage, setPromptMessage] = useState<string | undefined>();

  const { driverLocation } = useRideSocket(tripId, {
    onArrived: () => {
      if (rideDetails.tripType === "MECHANIC") {
        router.replace("/");
      } else {
        setPromptMessage("Driver has arrived at the pickup location");
      }
    },
    onPickedUp: () => {
      if (rideDetails.tripType === "ILLEGAL_PARKING") {
        router.replace("/");
      } else {
        setPromptMessage("Vehicle has been picked up");
      }
    },
    onDroppedOff: () => {
      router.replace("/");
    },
  });
  const { data: rideDetails } = useSuspenseQuery(queries.rides.detail(tripId));
  const origin = parseCoords(rideDetails.origin);
  const destination = parseCoords(rideDetails.destination);
  const mapRef = useRef<MapView>(null);

  const showDestination = rideDetails.tripType === "TOWING";

  //TODO: call the trip endpoint to get the trip details
  // if trip is in progress, connect to websocket of that trip
  // otherwise navigate to the trip summary page

  //TODO: show a map view with the pickup location, user;s current location and the drivers current location.

  //TODO: when the driver arrives at the pickup location, show a prompt to the user that the driver has arrived.
  // if the trip type is Road Side assistance: close the trip.

  //TODO: On Vehicle picked up, If request type is
  // 1. Illegal parking: show a prompt to the user that the vehicle has been picked up. and close the trip.
  // 2. Destination: keep showing the user the live location of the driver until the driver reaches the destination. and drops the vehicle.

  useEffect(() => {
    if (driverLocation) {
      // if the driver location is available, show the driver location on the map
      mapRef.current?.fitToCoordinates(
        [
          driverLocation,
          origin ?? currentLocation,
          destination ?? currentLocation,
        ],
        {
          edgePadding: {
            top: 100,
            right: 100,
            bottom: 100,
            left: 100,
          },
        },
      );
    }
  }, [driverLocation, origin, currentLocation, destination]);

  return (
    <XView
      style={{
        flex: 1,
      }}
    >
      <Text>Trip Id: {tripId}</Text>
      <Text>Showing live location of the driver coming from the websocket</Text>
      <MapView
        ref={mapRef}
        initialRegion={{
          // latitude: currentLocation.latitude,
          // longitude: currentLocation.longitude,
          latitude: origin?.latitude ?? currentLocation.latitude,
          longitude: origin?.longitude ?? currentLocation.longitude,
          latitudeDelta: 0.005,
          longitudeDelta: 0.005,
        }}
        showsUserLocation
        style={{
          width: "100%",
          height: "100%",
        }}
      >
        {driverLocation && (
          <Marker coordinate={driverLocation}>
            <Image
              style={{
                width: 40,
                height: 50,
              }}
              source={require("@x/rn-shared/assets/images/logo.png")}
              resizeMode="contain"
            />
          </Marker>
        )}
        {origin && <Marker coordinate={origin} pinColor="red" />}
        {showDestination && destination && (
          <Marker coordinate={destination} pinColor="green" />
        )}
      </MapView>
      <Snackbar
        visible={!!promptMessage}
        onDismiss={() => setPromptMessage(undefined)}
      >
        {promptMessage}
      </Snackbar>
    </XView>
  );
}
