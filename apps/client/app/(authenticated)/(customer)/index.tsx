import { Image, View } from "react-native";
import ServiceButton from "@/features/home-page/components/service-button";
import { Text } from "react-native-paper";
import { Redirect, useRouter } from "expo-router";
import { RideType } from "@x/rn-shared";
import { useSuspenseQuery } from "@tanstack/react-query";
import { queries } from "@x/api";

export default function CustomerHome() {
  const { navigate } = useRouter();
  const { data: userProfile } = useSuspenseQuery(queries.users.me);

  function handleRequestRide(type: RideType) {
    navigate(`/request-ride?type=${type}`);
  }

  if (!userProfile.firstName || !userProfile.lastName) {
    return (
      <Redirect
        href={{
          pathname: "/menu/profile",
          params: {
            redirectTo: "/",
          },
        }}
      />
    );
  }

  return (
    <View
      style={{
        flex: 1,
        justifyContent: "flex-end",
        alignItems: "center",
        gap: 32,
        paddingBottom: 64,
        paddingHorizontal: 32,
      }}
    >
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Image
          source={require("@x/rn-shared/assets/images/logo.png")}
          style={{
            aspectRatio: 1,
            width: 200,
          }}
          resizeMode="contain"
        />
      </View>
      <Text style={{ fontSize: 18, fontWeight: "700" }}>
        Request a Tow Truck for
      </Text>
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
          gap: 8,
        }}
      >
        <ServiceButton
          icon="map-marker-path"
          onPress={() => handleRequestRide("TOWING")}
        >
          Destination
        </ServiceButton>
        <ServiceButton
          icon="parking"
          onPress={() => handleRequestRide("ILLEGAL_PARKING")}
        >
          Illegal Parking
        </ServiceButton>
        <ServiceButton
          icon="account-wrench"
          onPress={() => handleRequestRide("MECHANIC")}
        >
          Road Side Assistance
        </ServiceButton>
      </View>
    </View>
  );
}
