import { <PERSON><PERSON><PERSON><PERSON>, XView } from "@x/rn-shared";
import { useSuspenseQuery } from "@tanstack/react-query";
import { components, queries, useUpdateProfile } from "@x/api";
import { Button, TextInput, useTheme } from "react-native-paper";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  RelativePathString,
  useLocalSearchParams,
  useRouter,
} from "expo-router";

const updateProfileSchema = z.object({
  firstName: z.string().nonempty(),
  lastName: z.string().nonempty(),
});

export default function CustomerProfilePage() {
  const theme = useTheme();
  const { redirectTo } = useLocalSearchParams<{
    redirectTo?: RelativePathString;
  }>();
  const { mutateAsync: updateUserProfile } = useUpdateProfile();
  const { data: userProfile } = useSuspenseQuery(queries.users.me);
  const router = useRouter();

  const {
    control,
    handleSubmit,
    formState: { isSubmitting, errors },
  } = useForm({
    defaultValues: {
      firstName: userProfile.firstName ?? "",
      lastName: userProfile.lastName ?? "",
    },
    resolver: zodResolver(updateProfileSchema),
  });

  async function onSubmit(data: components["schemas"]["UpdateCurrentUserDto"]) {
    await updateUserProfile(data);
    if (redirectTo) {
      router.replace(redirectTo);
    }
  }

  return (
    <XView
      style={{
        paddingVertical: 32,
        paddingHorizontal: 8,
        flexDirection: "column",
        gap: 12,
        alignItems: "center",
      }}
    >
      <XAvatar
        uri={userProfile.profilePic}
        style={{
          width: 100,
          height: 100,
          borderRadius: 16,
        }}
      />
      <XView
        style={{
          flexDirection: "column",
          gap: 16,
          width: "100%",
          maxWidth: 300,
        }}
      >
        <Controller
          render={({ field }) => (
            <TextInput
              label="First Name"
              value={field.value}
              mode="outlined"
              onChangeText={field.onChange}
              onBlur={field.onBlur}
              error={!!errors.firstName?.message}
            />
          )}
          name="firstName"
          control={control}
        />

        <Controller
          render={({ field }) => (
            <TextInput
              label="Last Name"
              value={field.value}
              mode="outlined"
              onChangeText={field.onChange}
              onBlur={field.onBlur}
              error={!!errors.lastName?.message}
            />
          )}
          name="lastName"
          control={control}
        />

        <TextInput
          label="Email"
          value={userProfile.email}
          mode="outlined"
          disabled
        />

        <Button
          mode="contained"
          onPress={handleSubmit(onSubmit)}
          loading={isSubmitting}
        >
          Save
        </Button>
      </XView>
    </XView>
  );
}
