import { XAvatar, XView } from "@x/rn-shared";
import { Divider, List, Text } from "react-native-paper";
import { useSuspenseQuery } from "@tanstack/react-query";
import { queries } from "@x/api";
import { useAuth } from "@x/auth";
import { useRouter } from "expo-router";

export default function MenuPage() {
  const { data: userProfile } = useSuspenseQuery(queries.users.me);
  const { logout } = useAuth();
  const router = useRouter();

  return (
    <XView
      style={{
        paddingVertical: 8,
        flexDirection: "column",
        gap: 12,
      }}
    >
      <XView
        style={{
          flexDirection: "row",
          alignItems: "center",
          gap: 8,
          paddingHorizontal: 24,
        }}
      >
        <XAvatar uri={userProfile?.profilePic} />
        <XView style={{ flexDirection: "column" }}>
          <Text variant="titleMedium">{`${userProfile.firstName} ${userProfile.lastName}`}</Text>
          <Text>{`${userProfile.email}`}</Text>
        </XView>
      </XView>
      <Divider />
      <List.Item
        onPress={() => router.navigate("/menu/profile")}
        title="Profile"
        left={(props) => <List.Icon {...props} icon="account" />}
      />
      {/*<List.Item*/}
      {/*  onPress={() => router.navigate("/menu/history")}*/}
      {/*  title="History"*/}
      {/*  left={(props) => <List.Icon {...props} icon="history" />}*/}
      {/*/>*/}
      {/*<List.Item*/}
      {/*  onPress={() => router.navigate("/menu/help")}*/}
      {/*  title="Help"*/}
      {/*  left={(props) => <List.Icon {...props} icon="lifebuoy" />}*/}
      {/*/>*/}
      <List.Item
        onPress={() => router.navigate("/menu/privacy-policy")}
        title="Privacy Policy"
        left={(props) => <List.Icon {...props} icon="shield-account" />}
      />
      <List.Item
        onPress={() => router.navigate("/menu/terms")}
        title="Terms and Conditions"
        left={(props) => <List.Icon {...props} icon="information" />}
      />
      <List.Item
        onPress={logout}
        title="Logout"
        titleStyle={{ color: "red" }}
        left={(props) => <List.Icon {...props} color="red" icon="logout" />}
      />
    </XView>
  );
}
