import { XView } from "@x/rn-shared";
import { Text } from "react-native-paper";
import { View } from "react-native";
import { useSuspenseQuery } from "@tanstack/react-query";
import { queries } from "@x/api";

export default function HistoryPage() {
  const { data: myTrips } = useSuspenseQuery(queries.trips.customerTripsMe);

  return (
    <XView style={{ flex: 1 }}>
      <Text variant="headlineMedium" style={{ paddingHorizontal: 16 }}>
        History
      </Text>
      <View style={{ paddingHorizontal: 16, flex: 1 }}>
        <Text>{JSON.stringify(myTrips, null, 2)}</Text>
      </View>
    </XView>
  );
}
