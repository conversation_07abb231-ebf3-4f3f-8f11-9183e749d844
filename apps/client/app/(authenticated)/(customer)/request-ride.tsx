import { View } from "react-native";
import { Text } from "react-native-paper";
import { useLocalSearchParams } from "expo-router";
import MapView, { Mark<PERSON>, PROVIDER_GOOGLE, Region } from "react-native-maps";
import { useEffect, useRef, useState } from "react";
import {
  Coordinates,
  coordsToString,
  RideType,
  useCurrentLocation,
} from "@x/rn-shared";
import { components, useCalculateCost } from "@x/api";
import MapViewRoute from "react-native-maps-directions";
import RideDetailsPrompt from "@/features/request-ride/components/ride-details-prompt";
import RequestTripDestinationPrompt from "@/features/request-ride/components/request-trip-destination-prompt";
import RideUpdatesPrompt from "@/features/request-ride/components/ride-updates-prompt";
import CarDetailsForm from "@/features/request-ride/components/car-details-form";

export default function RequestRidePage() {
  const mapRef = useRef<MapView>(null);
  const { type } = useLocalSearchParams<{
    type: RideType;
  }>();

  const key = process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY;
  if (!key) throw new Error("Google Maps API key not found");

  const { data: currentLocation } = useCurrentLocation();
  const [pickupLocation, setPickupLocation] = useState<Coordinates | null>(
    null,
  );
  const [destinationLocation, setDestinationLocation] =
    useState<Coordinates | null>(null);

  useEffect(() => {
    if (pickupLocation && destinationLocation) {
      mapRef.current?.fitToCoordinates([pickupLocation, destinationLocation], {
        edgePadding: { top: 100, right: 100, bottom: 100, left: 100 },
      });
    }
  }, [pickupLocation, destinationLocation]);

  const [region, setRegion] = useState<Region | undefined>();
  const [rideId, setRideId] = useState<string | undefined>();
  const [carDetailsCompleted, setCarDetailsCompleted] = useState<boolean>(false);

  const [rideEstimate, setRideEstimate] = useState<
    components["schemas"]["RideCalculationDto"] | undefined
  >(undefined);

  const { mutateAsync: calculateRideCost, isPending: isCalculatingCost } =
    useCalculateCost();

  if (type !== "TOWING" && type !== "ILLEGAL_PARKING" && type !== "MECHANIC") {
    return <Text>Invalid ride type</Text>;
  }

  const shouldAskForDestination = type === "TOWING";
  const showLocationPickerMarker =
    !pickupLocation || (shouldAskForDestination && !destinationLocation);

  async function handleRequestRide() {
    if (!pickupLocation) return;

    const res = await calculateRideCost({
      origin: coordsToString(pickupLocation),
      destination: coordsToString(destinationLocation ?? pickupLocation),
      TripType: type,
    });

    setRideEstimate(res);
  }

  function handleOfferDismiss() {
    setRideEstimate(undefined);
    setPickupLocation(null);
    setDestinationLocation(null);
  }

  function handleCarDetailsDismiss() {
    setRideId(undefined);
    setCarDetailsCompleted(false);
    setPickupLocation(null);
    setDestinationLocation(null);
  }

  return (
    <View style={{ flex: 1 }}>
      <View style={{ flex: 1 }}>
        <MapView
          provider={PROVIDER_GOOGLE}
          ref={mapRef}
          style={{ width: "100%", height: "100%" }}
          initialRegion={{
            latitude: currentLocation.latitude,
            longitude: currentLocation.longitude,
            latitudeDelta: 0.005,
            longitudeDelta: 0.005,
          }}
          onRegionChange={setRegion}
          showsUserLocation
        >
          {/*Location Picker Marker*/}
          {showLocationPickerMarker && (
            <Marker coordinate={region ?? currentLocation} pinColor="blue" />
          )}

          {/*Pickup marker*/}
          {pickupLocation && (
            <Marker
              coordinate={pickupLocation}
              title="Pickup"
              pinColor="wheat"
            />
          )}

          {/*Destination Marker*/}
          {destinationLocation && (
            <Marker
              coordinate={destinationLocation}
              title="Destination"
              pinColor="red"
            />
          )}

          {/*Route*/}
          {pickupLocation && destinationLocation && (
            <MapViewRoute
              origin={pickupLocation}
              destination={destinationLocation}
              apikey={key}
              strokeColor="black"
              strokeWidth={4}
            />
          )}
        </MapView>
      </View>

      {!rideId && !rideEstimate && (
        <RequestTripDestinationPrompt
          requestType={type}
          pickupLocation={pickupLocation}
          destinationLocation={destinationLocation}
          onConfirmPickup={() => setPickupLocation(region ?? currentLocation)}
          onConfirmDestination={() =>
            setDestinationLocation(region ?? currentLocation)
          }
          onRequestRide={handleRequestRide}
          isCalculatingCost={isCalculatingCost}
        />
      )}

      {rideEstimate && (
        <RideDetailsPrompt
          rideEstimate={rideEstimate}
          onDismiss={handleOfferDismiss}
          onRideCreated={(rideId) => {
            setRideEstimate(undefined);
            setRideId(rideId);
          }}
        />
      )}

      {rideId && !carDetailsCompleted && (
        <CarDetailsForm
          rideId={rideId}
          onCarDetailsSubmitted={() => setCarDetailsCompleted(true)}
          onDismiss={handleCarDetailsDismiss}
        />
      )}

      {rideId && carDetailsCompleted && (
        <RideUpdatesPrompt rideId={rideId} rideType={type} />
      )}
    </View>
  );
}
