import { Redirect, Slot } from "expo-router";
import { View } from "react-native";
import { AppBar } from "@/features/common/components/app-bar";
import { useAuth } from "@x/auth";

export default function AuthenticatedLayout() {
  const { user } = useAuth();
  if (!user) {
    return <Redirect href="/login" />;
  }

  return (
    <View
      style={{
        flex: 1,
      }}
    >
      <AppBar />
      <Slot />
    </View>
  );
}
