import { Image, StyleSheet, View } from "react-native";
import React from "react";
import { Text } from "react-native-paper";
import { GoogleLoginButton, SignUpForm, XDivider } from "@x/rn-shared";
import { Link } from "expo-router";
import { XKbdView } from "@x/rn-shared/components/templates/x-kbd-view";

export default function SignupPage() {
  return (
    <XKbdView center>
      {/* Logo Section */}
      <Image
        source={require("@x/rn-shared/assets/images/logo.png")}
        style={styles.logo}
        resizeMode="contain"
      />

      <SignUpForm />

      {/* Sign in Link */}
      <Text variant="bodySmall" style={{ alignSelf: "center" }}>
        Already have an account?{" "}
        <Link
          replace
          href="/login"
          style={{
            fontWeight: "bold",
          }}
        >
          Sign in
        </Link>
      </Text>

      {/* Divider */}
      <XDivider text="or continue with" style={{ marginVertical: 32 }} />

      {/* Social Login Buttons */}
      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-around",
          width: "100%",
        }}
      >
        <GoogleLoginButton />
      </View>
    </XKbdView>
  );
}

const styles = StyleSheet.create({
  logo: {
    width: 150,
    height: 150,
  },
  socialLoginContainer: {
    flexDirection: "row",
    justifyContent: "space-around",
    width: "100%",
  },
  socialButton: {
    width: 50,
    height: 50,
    borderRadius: 100,
    justifyContent: "center",
    alignItems: "center",
  },
  socialIcon: {
    width: 40,
    height: 40,
  },
});
