import React from "react";
import { Image, StyleSheet, View } from "react-native";
import { Text } from "react-native-paper";
import { Link } from "expo-router";
import { GoogleLoginButton, SignInForm, XDivider } from "@x/rn-shared";
import { XKbdView } from "@x/rn-shared/components/templates/x-kbd-view";

export default function LoginScreen() {
  return (
    <XKbdView center>
      {/* Logo Section */}
      <Image
        source={require("@x/rn-shared/assets/images/logo.png")}
        style={styles.logo}
        resizeMode="contain"
      />

      <SignInForm />

      {/* Signup Link */}
      <Text variant="bodySmall" style={{ alignSelf: "center" }}>
        Don't have an account?{" "}
        <Link
          href="/signup"
          style={{
            fontWeight: "bold",
          }}
        >
          Sign up
        </Link>
      </Text>

      {/* Divider */}
      <XDivider text="or continue with" style={{ marginVertical: 32 }} />

      {/* Social Login Buttons */}
      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-around",
          width: "100%",
        }}
      >
        <GoogleLoginButton />
      </View>
    </XKbdView>
  );
}

const styles = StyleSheet.create({
  logo: {
    width: 150,
    height: 150,
  },
});
