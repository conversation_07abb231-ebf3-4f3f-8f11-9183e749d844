import { Appbar } from "react-native-paper";
import { Image, TouchableOpacity, View } from "react-native";
import { usePathname, useRouter } from "expo-router";
import { useMemo } from "react";
import { useSuspenseQuery } from "@tanstack/react-query";
import { queries } from "@x/api";
import { XAvatar } from "@x/rn-shared";

export function AppBar() {
  const { data: userProfile } = useSuspenseQuery(queries.users.me);
  const router = useRouter();
  const pathname = usePathname();

  function handleChatPress() {
    router.navigate("/chat-bot");
  }

  function handleBackButton() {
    router.back();
  }

  const canGoBack = useMemo(() => {
    // Don't show back button on home page
    if (pathname === "/") return false;
    return router.canGoBack();
  }, [router, pathname]);

  return (
    <Appbar.Header mode="center-aligned" style={{ paddingLeft: 16 }}>
      {canGoBack ? (
        <Appbar.BackAction onPress={handleBackButton} />
      ) : (
        <TouchableOpacity
          onPress={() => {
            router.navigate("/menu");
          }}
        >
          <XAvatar uri={userProfile?.profilePic} />
        </TouchableOpacity>
      )}
      <Appbar.Content
        mode="center-aligned"
        style={{ height: 64, position: "static" }}
        title={
          <View
            style={{
              position: "absolute",
              left: 0,
              right: 0,
              alignItems: "center",
            }}
          >
            <Image
              source={require("@x/rn-shared/assets/images/logo.png")}
              resizeMode="contain"
              style={{
                aspectRatio: 1,
                height: 60,
              }}
            />
          </View>
        }
      />
      {/*<Appbar.Action icon="chat" onPress={handleChatPress} />*/}
    </Appbar.Header>
  );
}
