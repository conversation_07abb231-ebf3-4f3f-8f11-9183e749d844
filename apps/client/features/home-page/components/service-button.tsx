import { Button, ButtonProps, Icon, Text } from "react-native-paper";
import { PropsWithChildren } from "react";
import { StyleSheet, View } from "react-native";
import { IconSource } from "react-native-paper/lib/typescript/components/Icon";

interface ServiceButtonProps extends ButtonProps {
  icon: IconSource;
  iconSize?: number;
}

export default function ServiceButton({
  children,
  ...buttonProps
}: PropsWithChildren<ServiceButtonProps>) {
  return (
    <Button
      mode="outlined"
      compact
      style={styles.buttonStyle}
      contentStyle={styles.contentStyle}
      {...buttonProps}
      icon={({ color }) => (
        <Icon
          source={buttonProps.icon}
          size={buttonProps.iconSize ?? 32}
          color={color}
        />
      )}
    >
      <View>
        <Text
          numberOfLines={2}
          variant="labelMedium"
          style={{ textAlign: "center" }}
        >
          {children}
        </Text>
      </View>
    </Button>
  );
}

const styles = StyleSheet.create({
  buttonStyle: {
    flex: 1,
    aspectRatio: 1,
    // hardcode the size to 110x110 because otherwise they mess up the layout
    minWidth: 105,
    maxWidth: 105,
    minHeight: 105,
    maxHeight: 105,
  },
  contentStyle: {
    aspectRatio: 1,
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
  },
});
