import { Button, <PERSON> } from "react-native-paper";
import { useRouter } from "expo-router";
import { useCallback, useEffect, useState } from "react";
import { WS_URL } from "@x/constants";
import { io, Socket } from "socket.io-client";
import { useAuth } from "@x/auth";
import { RideType, XView } from "@x/rn-shared";

interface RideUpdatePromptProps {
  rideId: string;
  rideType: RideType;
}

enum RideStatus {
  CONNECTING = "CONNECTING",
  LOOKING_FOR_DRIVER = "LOOKING_FOR_DRIVER",
  NO_DRIVER_AVAILABLE = "NO_DRIVER_AVAILABLE",
  RIDE_ACCEPTED_BY_DRIVER = "RIDE_ACCEPTED_BY_DRIVER",
  DRIVER_FOUND_WAITING_CONFIRMATION = "DRIVER_FOUND_WAITING_CONFIRMATION",
}

export default function RideUpdatesPrompt({
  rideId,
  rideType,
}: RideUpdatePromptProps) {
  // TODO: it should connect to websocket and show updates to the user for that particular ride

  const router = useRouter();
  const { user } = useAuth();
  const [socket, setSocket] = useState<Socket | null>(null);
  const [token, setToken] = useState<string | undefined>();
  const [state, setState] = useState<RideStatus>(RideStatus.CONNECTING);

  useEffect(() => {
    const getToken = async () => {
      const token = await user?.getIdToken();
      setToken(token);
    };
    void getToken();
  }, [rideId]);

  const handleMessage = useCallback(
    (message: string) => {
      if (message === "NO_DRIVER_AVAILABLE") {
        setState(RideStatus.NO_DRIVER_AVAILABLE);
      }

      if (message === "LOOKING_FOR_DRIVER") {
        setState(RideStatus.LOOKING_FOR_DRIVER);
      }

      if (message === "DRIVER_FOUND_WAITING_CONFIRMATION") {
        setState(RideStatus.DRIVER_FOUND_WAITING_CONFIRMATION);
      }

      if (message.startsWith("RIDE_ACCEPTED_BY_DRIVER")) {
        setState(RideStatus.RIDE_ACCEPTED_BY_DRIVER);
        router.replace({
          pathname: "/trips/[tripId]/progress",
          params: {
            tripId: rideId,
          },
        });
      }
    },
    [router, rideId],
  );

  useEffect(() => {
    if (!token) return;

    const socket = io(`${WS_URL}/ride?rideId=${rideId}`, {
      extraHeaders: {
        Authorization: `Bearer ${token}`,
      },
    });

    const connection = socket.connect();
    setSocket(connection);

    socket.on("connect", () => {
      console.log("Connected to server", "hello");
    });

    socket.on("disconnect", () => {
      console.log("Disconnected from server");
    });

    socket.on("error", (error) => {
      console.error("Error from server", error);
    });

    socket.on("message", (message) => {
      handleMessage(message);
      console.log("Message from server", message);
    });

    socket.onAny((event, ...args) => {
      console.log(event, args);
    });

    return () => {
      connection.disconnect();
    };
  }, [token, handleMessage]);

  const lookingFor = rideType === "MECHANIC" ? "Mechanic" : "Driver";

  const stateToText = {
    [RideStatus.CONNECTING]: "Connecting...",
    [RideStatus.LOOKING_FOR_DRIVER]: `Looking for ${lookingFor}...`,
    [RideStatus.NO_DRIVER_AVAILABLE]: `No ${lookingFor} available at the moment`,
    [RideStatus.RIDE_ACCEPTED_BY_DRIVER]: "Ride accepted by driver",
    [RideStatus.DRIVER_FOUND_WAITING_CONFIRMATION]:
      "Driver found, waiting for confirmation",
  };

  return (
    <XView
      style={{
        gap: 12,
        paddingHorizontal: 24,
        paddingVertical: 24,
      }}
    >
      <Text variant="titleLarge">{stateToText[state]}</Text>
      {state === "NO_DRIVER_AVAILABLE" && (
        <Button mode="contained" onPress={() => router.replace("/")}>
          Go Back
        </Button>
      )}
    </XView>
  );
}
