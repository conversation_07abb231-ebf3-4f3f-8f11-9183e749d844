import React, { useState } from "react";
import { <PERSON>, ScrollView, Alert } from "react-native";
import { Button, Text, TextInput, SegmentedButtons, Switch } from "react-native-paper";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import * as ImagePicker from "expo-image-picker";
import { XView } from "@x/rn-shared";
import { useCreateClientCar, components } from "@x/api";

const carDetailsSchema = z.object({
  model: z.string().min(1, "Car model is required"),
  registration: z.string().min(1, "Registration number is required"),
  vehicleType: z.enum(["CAR", "TRUCK"]),
  year: z.number().min(1900, "Invalid year").max(new Date().getFullYear() + 1, "Invalid year"),
  suspension: z.string().optional(),
  damageDescription: z.string().min(1, "Damage description is required"),
  isInsured: z.boolean(),
});

type CarDetailsFormData = z.infer<typeof carDetailsSchema> & {
  front?: string;
  back?: string;
  left?: string;
  right?: string;
};

interface CarDetailsFormProps {
  rideId: string;
  onCarDetailsSubmitted: () => void;
  onDismiss: () => void;
}

export default function CarDetailsForm({
  rideId,
  onCarDetailsSubmitted,
  onDismiss,
}: CarDetailsFormProps) {
  const [photos, setPhotos] = useState<{
    front?: string;
    back?: string;
    left?: string;
    right?: string;
  }>({});

  const { mutateAsync: createClientCar, isPending } = useCreateClientCar();

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    watch,
  } = useForm<CarDetailsFormData>({
    resolver: zodResolver(carDetailsSchema),
    mode: "onChange",
    defaultValues: {
      model: "",
      registration: "",
      vehicleType: "CAR",
      year: new Date().getFullYear(),
      suspension: "",
      damageDescription: "",
      isInsured: false,
    },
  });

  const vehicleType = watch("vehicleType");

  const requestPermission = async () => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== "granted") {
      Alert.alert("Permission needed", "Please grant camera roll permissions to upload photos");
      return false;
    }
    return true;
  };

  const pickImage = async (photoType: keyof typeof photos) => {
    const hasPermission = await requestPermission();
    if (!hasPermission) return;

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      setPhotos(prev => ({
        ...prev,
        [photoType]: result.assets[0].uri,
      }));
    }
  };

  const takePhoto = async (photoType: keyof typeof photos) => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== "granted") {
      Alert.alert("Permission needed", "Please grant camera permissions to take photos");
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      setPhotos(prev => ({
        ...prev,
        [photoType]: result.assets[0].uri,
      }));
    }
  };

  const showImagePicker = (photoType: keyof typeof photos) => {
    Alert.alert(
      "Select Photo",
      "Choose how you want to add the photo",
      [
        { text: "Camera", onPress: () => takePhoto(photoType) },
        { text: "Gallery", onPress: () => pickImage(photoType) },
        { text: "Cancel", style: "cancel" },
      ]
    );
  };

  const onSubmit = async (data: CarDetailsFormData) => {
    try {
      // Check if required photos are present
      if (!photos.front || !photos.back) {
        Alert.alert("Missing Photos", "Please add both front and back photos of your vehicle");
        return;
      }

      // For now, using the local URIs as placeholders
      // In production, these would be uploaded to Firebase Storage first
      const frontUrl = photos.front || "https://via.placeholder.com/400x300?text=Front+Photo";
      const backUrl = photos.back || "https://via.placeholder.com/400x300?text=Back+Photo";
      const leftUrl = photos.left || "";
      const rightUrl = photos.right || "";

      await createClientCar({
        model: data.model,
        registration: data.registration,
        vehicleType: data.vehicleType,
        year: data.year,
        suspension: data.suspension || "",
        damageDescription: data.damageDescription,
        front: frontUrl,
        back: backUrl,
        left: leftUrl,
        right: rightUrl,
        isInsured: data.isInsured,
        tripId: rideId,
      });

      onCarDetailsSubmitted();
    } catch (error) {
      console.error("Error submitting car details:", error);
      Alert.alert("Error", "Failed to submit car details. Please try again.");
    }
  };

  return (
    <XView style={{ flex: 1, paddingHorizontal: 24, paddingVertical: 16 }}>
      <Text variant="headlineSmall" style={{ marginBottom: 16 }}>
        Vehicle Details
      </Text>
      
      <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
        {/* Vehicle Type */}
        <View style={{ marginBottom: 16 }}>
          <Text variant="labelLarge" style={{ marginBottom: 8 }}>
            Vehicle Type *
          </Text>
          <Controller
            name="vehicleType"
            control={control}
            render={({ field: { onChange, value } }) => (
              <SegmentedButtons
                value={value}
                onValueChange={onChange}
                buttons={[
                  { value: "CAR", label: "Car" },
                  { value: "TRUCK", label: "Truck" },
                ]}
              />
            )}
          />
        </View>

        {/* Model */}
        <View style={{ marginBottom: 16 }}>
          <Controller
            name="model"
            control={control}
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                label="Vehicle Model *"
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                error={!!errors.model}
                placeholder="e.g., Toyota Camry, Ford F-150"
              />
            )}
          />
          {errors.model && (
            <Text variant="bodySmall" style={{ color: "red", marginTop: 4 }}>
              {errors.model.message}
            </Text>
          )}
        </View>

        {/* Registration */}
        <View style={{ marginBottom: 16 }}>
          <Controller
            name="registration"
            control={control}
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                label="Registration Number *"
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                error={!!errors.registration}
                placeholder="e.g., ABC-123"
                autoCapitalize="characters"
              />
            )}
          />
          {errors.registration && (
            <Text variant="bodySmall" style={{ color: "red", marginTop: 4 }}>
              {errors.registration.message}
            </Text>
          )}
        </View>

        {/* Year */}
        <View style={{ marginBottom: 16 }}>
          <Controller
            name="year"
            control={control}
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                label="Year *"
                value={value.toString()}
                onChangeText={(text) => onChange(parseInt(text) || new Date().getFullYear())}
                onBlur={onBlur}
                error={!!errors.year}
                keyboardType="numeric"
                placeholder="e.g., 2020"
              />
            )}
          />
          {errors.year && (
            <Text variant="bodySmall" style={{ color: "red", marginTop: 4 }}>
              {errors.year.message}
            </Text>
          )}
        </View>

        {/* Suspension */}
        <View style={{ marginBottom: 16 }}>
          <Controller
            name="suspension"
            control={control}
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                label="Suspension Type (Optional)"
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                placeholder="e.g., Air Suspension, Standard"
              />
            )}
          />
        </View>

        {/* Damage Description */}
        <View style={{ marginBottom: 16 }}>
          <Controller
            name="damageDescription"
            control={control}
            render={({ field: { onChange, onBlur, value } }) => (
              <TextInput
                label="Damage Description *"
                value={value}
                onChangeText={onChange}
                onBlur={onBlur}
                error={!!errors.damageDescription}
                multiline
                numberOfLines={3}
                placeholder="Describe any visible damage to your vehicle"
              />
            )}
          />
          {errors.damageDescription && (
            <Text variant="bodySmall" style={{ color: "red", marginTop: 4 }}>
              {errors.damageDescription.message}
            </Text>
          )}
        </View>

        {/* Insurance */}
        <View style={{ marginBottom: 24, flexDirection: "row", alignItems: "center", justifyContent: "space-between" }}>
          <Text variant="labelLarge">Vehicle is insured</Text>
          <Controller
            name="isInsured"
            control={control}
            render={({ field: { onChange, value } }) => (
              <Switch value={value} onValueChange={onChange} />
            )}
          />
        </View>

        {/* Photo Section */}
        <Text variant="labelLarge" style={{ marginBottom: 16 }}>
          Vehicle Photos *
        </Text>
        
        <View style={{ gap: 12, marginBottom: 24 }}>
          {/* Front Photo */}
          <View>
            <Button
              mode={photos.front ? "contained" : "outlined"}
              onPress={() => showImagePicker("front")}
              icon="camera"
            >
              {photos.front ? "Front Photo Added ✓" : "Add Front Photo *"}
            </Button>
          </View>

          {/* Back Photo */}
          <View>
            <Button
              mode={photos.back ? "contained" : "outlined"}
              onPress={() => showImagePicker("back")}
              icon="camera"
            >
              {photos.back ? "Back Photo Added ✓" : "Add Back Photo *"}
            </Button>
          </View>

          {/* Left Photo */}
          <View>
            <Button
              mode={photos.left ? "contained" : "outlined"}
              onPress={() => showImagePicker("left")}
              icon="camera"
            >
              {photos.left ? "Left Side Photo Added ✓" : "Add Left Side Photo (Optional)"}
            </Button>
          </View>

          {/* Right Photo */}
          <View>
            <Button
              mode={photos.right ? "contained" : "outlined"}
              onPress={() => showImagePicker("right")}
              icon="camera"
            >
              {photos.right ? "Right Side Photo Added ✓" : "Add Right Side Photo (Optional)"}
            </Button>
          </View>
        </View>
      </ScrollView>

      {/* Action Buttons */}
      <View style={{ flexDirection: "row", gap: 12, marginTop: 16 }}>
        <Button
          mode="outlined"
          onPress={onDismiss}
          style={{ flex: 1 }}
          disabled={isPending}
        >
          Cancel
        </Button>
        <Button
          mode="contained"
          onPress={handleSubmit(onSubmit)}
          style={{ flex: 1 }}
          loading={isPending}
          disabled={!isValid || !photos.front || !photos.back}
        >
          Submit
        </Button>
      </View>
    </XView>
  );
}
