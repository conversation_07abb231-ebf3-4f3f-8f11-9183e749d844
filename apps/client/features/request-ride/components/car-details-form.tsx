import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>ie<PERSON>, Al<PERSON>, StyleSheet } from "react-native";
import { Button, Text, TextInput, SegmentedButtons, Switch } from "react-native-paper";
import * as ImagePicker from "expo-image-picker";
import { useCreateClientCar } from "@x/api";

interface CarDetailsFormData {
  model: string;
  registration: string;
  vehicleType: "CAR" | "TRUCK";
  year: string;
  suspension: string;
  damageDescription: string;
  isInsured: boolean;
}

interface CarDetailsFormProps {
  rideId: string;
  onCarDetailsSubmitted: () => void;
  onDismiss: () => void;
}

export default function CarDetailsForm({
  rideId,
  onCarDetailsSubmitted,
  onDismiss,
}: CarDetailsFormProps) {
  const { mutateAsync: createClientCar, isPending } = useCreateClientCar();
  
  const [formData, setFormData] = useState<CarDetailsFormData>({
    model: "",
    registration: "",
    vehicleType: "CAR",
    year: "",
    suspension: "",
    damageDescription: "",
    isInsured: false,
  });

  const [photos, setPhotos] = useState<{
    front?: string;
    back?: string;
    left?: string;
    right?: string;
  }>({});

  const [errors, setErrors] = useState<Partial<CarDetailsFormData>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<CarDetailsFormData> = {};
    
    if (!formData.model.trim()) newErrors.model = "Car model is required";
    if (!formData.registration.trim()) newErrors.registration = "Registration is required";
    if (!formData.year.trim()) newErrors.year = "Year is required";
    if (!formData.damageDescription.trim()) newErrors.damageDescription = "Damage description is required";
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const updateFormData = (field: keyof CarDetailsFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const takePhoto = async (photoType: keyof typeof photos) => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== "granted") {
      Alert.alert("Permission needed", "Please grant camera permissions to take photos");
      return;
    }

    const result = await ImagePicker.launchCameraAsync({
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      setPhotos(prev => ({
        ...prev,
        [photoType]: result.assets[0].uri,
      }));
    }
  };

  const pickImage = async (photoType: keyof typeof photos) => {
    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== "granted") {
      Alert.alert("Permission needed", "Please grant camera roll permissions to upload photos");
      return;
    }

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.8,
    });

    if (!result.canceled && result.assets[0]) {
      setPhotos(prev => ({
        ...prev,
        [photoType]: result.assets[0].uri,
      }));
    }
  };

  const showImagePicker = (photoType: keyof typeof photos) => {
    Alert.alert(
      "Select Photo",
      "Choose how you want to add the photo",
      [
        { text: "Camera", onPress: () => takePhoto(photoType) },
        { text: "Gallery", onPress: () => pickImage(photoType) },
        { text: "Cancel", style: "cancel" },
      ]
    );
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;
    
    // Check if required photos are present
    if (!photos.front || !photos.back) {
      Alert.alert("Missing Photos", "Please add both front and back photos of your vehicle");
      return;
    }

    try {
      // For now, using the local URIs as placeholders
      // In production, these would be uploaded to Firebase Storage first
      const frontUrl = photos.front || "https://via.placeholder.com/400x300?text=Front+Photo";
      const backUrl = photos.back || "https://via.placeholder.com/400x300?text=Back+Photo";
      const leftUrl = photos.left || "";
      const rightUrl = photos.right || "";

      await createClientCar({
        model: formData.model,
        registration: formData.registration,
        vehicleType: formData.vehicleType,
        year: parseInt(formData.year),
        suspension: formData.suspension || "",
        damageDescription: formData.damageDescription,
        front: frontUrl,
        back: backUrl,
        left: leftUrl,
        right: rightUrl,
        isInsured: formData.isInsured,
        tripId: rideId,
      });

      onCarDetailsSubmitted();
    } catch (error) {
      console.error("Error submitting car details:", error);
      Alert.alert("Error", "Failed to submit car details. Please try again.");
    }
  };

  const vehicleTypeOptions = [
    { value: "CAR", label: "Car" },
    { value: "TRUCK", label: "Truck" },
  ];

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <Text variant="headlineSmall" style={styles.title}>
          Car Details
        </Text>
        <Text variant="bodyMedium" style={styles.subtitle}>
          Please provide details about your vehicle for the towing service
        </Text>

        {/* Car Model */}
        <View style={styles.inputContainer}>
          <TextInput
            label="Car Model"
            value={formData.model}
            onChangeText={(text) => updateFormData("model", text)}
            error={!!errors.model}
            style={styles.input}
          />
          {errors.model && <Text style={styles.errorText}>{errors.model}</Text>}
        </View>

        {/* Registration */}
        <View style={styles.inputContainer}>
          <TextInput
            label="Registration Number"
            value={formData.registration}
            onChangeText={(text) => updateFormData("registration", text)}
            error={!!errors.registration}
            style={styles.input}
          />
          {errors.registration && <Text style={styles.errorText}>{errors.registration}</Text>}
        </View>

        {/* Vehicle Type */}
        <View style={styles.inputContainer}>
          <Text variant="bodyMedium" style={styles.label}>Vehicle Type</Text>
          <SegmentedButtons
            value={formData.vehicleType}
            onValueChange={(value) => updateFormData("vehicleType", value)}
            buttons={vehicleTypeOptions}
            style={styles.segmentedButtons}
          />
        </View>

        {/* Year */}
        <View style={styles.inputContainer}>
          <TextInput
            label="Year"
            value={formData.year}
            onChangeText={(text) => updateFormData("year", text)}
            keyboardType="numeric"
            error={!!errors.year}
            style={styles.input}
          />
          {errors.year && <Text style={styles.errorText}>{errors.year}</Text>}
        </View>

        {/* Suspension */}
        <View style={styles.inputContainer}>
          <TextInput
            label="Suspension Type (Optional)"
            value={formData.suspension}
            onChangeText={(text) => updateFormData("suspension", text)}
            style={styles.input}
          />
        </View>

        {/* Damage Description */}
        <View style={styles.inputContainer}>
          <TextInput
            label="Damage Description"
            value={formData.damageDescription}
            onChangeText={(text) => updateFormData("damageDescription", text)}
            multiline
            numberOfLines={3}
            error={!!errors.damageDescription}
            style={styles.input}
          />
          {errors.damageDescription && <Text style={styles.errorText}>{errors.damageDescription}</Text>}
        </View>

        {/* Insurance */}
        <View style={styles.switchContainer}>
          <Text variant="bodyMedium">Is your vehicle insured?</Text>
          <Switch
            value={formData.isInsured}
            onValueChange={(value) => updateFormData("isInsured", value)}
          />
        </View>

        {/* Photo Section */}
        <Text variant="titleMedium" style={styles.photoSectionTitle}>
          Vehicle Photos
        </Text>
        <Text variant="bodySmall" style={styles.photoSubtitle}>
          Please add photos of your vehicle (Front and Back are required)
        </Text>

        <View style={styles.photoGrid}>
          {/* Front Photo */}
          <View style={styles.photoContainer}>
            <Text variant="bodySmall" style={styles.photoLabel}>Front *</Text>
            <Button
              mode={photos.front ? "contained" : "outlined"}
              onPress={() => showImagePicker("front")}
              style={styles.photoButton}
            >
              {photos.front ? "✓ Added" : "Add Photo"}
            </Button>
          </View>

          {/* Back Photo */}
          <View style={styles.photoContainer}>
            <Text variant="bodySmall" style={styles.photoLabel}>Back *</Text>
            <Button
              mode={photos.back ? "contained" : "outlined"}
              onPress={() => showImagePicker("back")}
              style={styles.photoButton}
            >
              {photos.back ? "✓ Added" : "Add Photo"}
            </Button>
          </View>

          {/* Left Photo */}
          <View style={styles.photoContainer}>
            <Text variant="bodySmall" style={styles.photoLabel}>Left</Text>
            <Button
              mode={photos.left ? "contained" : "outlined"}
              onPress={() => showImagePicker("left")}
              style={styles.photoButton}
            >
              {photos.left ? "✓ Added" : "Add Photo"}
            </Button>
          </View>

          {/* Right Photo */}
          <View style={styles.photoContainer}>
            <Text variant="bodySmall" style={styles.photoLabel}>Right</Text>
            <Button
              mode={photos.right ? "contained" : "outlined"}
              onPress={() => showImagePicker("right")}
              style={styles.photoButton}
            >
              {photos.right ? "✓ Added" : "Add Photo"}
            </Button>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <Button
            mode="contained"
            onPress={handleSubmit}
            loading={isPending}
            disabled={isPending}
            style={styles.submitButton}
          >
            Submit Car Details
          </Button>
          
          <Button
            mode="outlined"
            onPress={onDismiss}
            disabled={isPending}
            style={styles.cancelButton}
          >
            Cancel
          </Button>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  scrollView: {
    flex: 1,
    padding: 16,
    paddingTop: 60, // Account for status bar and provide some top spacing
  },
  title: {
    textAlign: "center",
    marginBottom: 8,
    fontWeight: "bold",
  },
  subtitle: {
    textAlign: "center",
    marginBottom: 24,
    color: "#666",
  },
  inputContainer: {
    marginBottom: 16,
  },
  input: {
    backgroundColor: "#fff",
  },
  label: {
    marginBottom: 8,
    fontWeight: "500",
  },
  errorText: {
    color: "#d32f2f",
    fontSize: 12,
    marginTop: 4,
  },
  segmentedButtons: {
    marginTop: 8,
  },
  switchContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 24,
    paddingVertical: 8,
  },
  photoSectionTitle: {
    marginBottom: 8,
    fontWeight: "600",
  },
  photoSubtitle: {
    marginBottom: 16,
    color: "#666",
  },
  photoGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    marginBottom: 24,
  },
  photoContainer: {
    width: "48%",
    marginBottom: 12,
  },
  photoLabel: {
    marginBottom: 8,
    fontWeight: "500",
  },
  photoButton: {
    borderRadius: 8,
  },
  buttonContainer: {
    marginTop: 16,
    gap: 12,
  },
  submitButton: {
    borderRadius: 8,
    paddingVertical: 4,
  },
  cancelButton: {
    borderRadius: 8,
    paddingVertical: 4,
  },
});
