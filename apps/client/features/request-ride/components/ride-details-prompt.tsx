import { Button, Text } from "react-native-paper";
import { View } from "react-native";
import { components, queries, useRequestRide } from "@x/api";
import { useSuspenseQuery } from "@tanstack/react-query";
import { XView } from "@x/rn-shared";

interface RideDetailPromptProps {
  rideEstimate: components["schemas"]["RideCalculationDto"];
  onRideCreated: (rideId: string) => void;
  onDismiss: () => void;
}

export default function RideDetailsPrompt({
  rideEstimate,
  onRideCreated,
  onDismiss,
}: RideDetailPromptProps) {
  const { data: userProfile } = useSuspenseQuery(queries.users.me);

  const { mutateAsync: requestRide, isPending: isRequestingRide } =
    useRequestRide();

  async function handleAcceptRequest() {
    if (!rideEstimate) return;
    const rideId = (await requestRide({
      calculationId: rideEstimate.id,
      paymentMethod: "CASH",
      userId: userProfile.id,
    })) as unknown as string;

    onRideCreated(rideId);
  }

  return (
    <XView
      style={{
        gap: 12,
        paddingHorizontal: 24,
        paddingVertical: 24,
      }}
    >
      <Text variant="headlineSmall">Trip Details</Text>
      <View>
        <Text variant="labelMedium">
          Estimated Time: {rideEstimate.durationInSeconds / 60} minutes
        </Text>
        <Text variant="labelMedium">
          Estimate Distance: {rideEstimate.distanceInMeters / 1000} km
        </Text>
        <Text variant="labelLarge">
          Total Fare: {rideEstimate.totalFare} {rideEstimate.currency}
        </Text>
      </View>
      <View
        style={{
          flexDirection: "row",
          gap: 8,
          justifyContent: "flex-end",
        }}
      >
        <Button mode="outlined" onPress={onDismiss}>
          Dismiss
        </Button>
        <Button
          mode="contained"
          icon="arrow-right"
          contentStyle={{ flexDirection: "row-reverse" }}
          loading={isRequestingRide}
          onPress={handleAcceptRequest}
        >
          Accept
        </Button>
      </View>
    </XView>
  );
}
