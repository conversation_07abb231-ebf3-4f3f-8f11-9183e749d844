import { Button } from "react-native-paper";
import { Coordinates, RideType, XView } from "@x/rn-shared";

interface RequestTripDestinationPromptProps {
  requestType: RideType;
  pickupLocation: Coordinates | null;
  destinationLocation: Coordinates | null;
  onRequestRide: () => void;
  onConfirmPickup: () => void;
  onConfirmDestination: () => void;
  isCalculatingCost: boolean;
}

export default function RequestTripDestinationPrompt({
  requestType,
  pickupLocation,
  destinationLocation,
  onRequestRide,
  onConfirmPickup,
  onConfirmDestination,
  isCalculatingCost,
}: RequestTripDestinationPromptProps) {
  const shouldAskForDestination = requestType === "TOWING";

  return (
    <XView
      style={{
        gap: 12,
        paddingHorizontal: 24,
        paddingVertical: 24,
      }}
    >
      {!pickupLocation && (
        <Button mode="contained" onPress={onConfirmPickup}>
          Confirm Pickup
        </Button>
      )}

      {pickupLocation && !destinationLocation && shouldAskForDestination && (
        <Button mode="contained" onPress={onConfirmDestination}>
          Confirm Destination
        </Button>
      )}

      {pickupLocation && (destinationLocation || !shouldAskForDestination) && (
        <Button
          mode="contained"
          onPress={onRequestRide}
          loading={isCalculatingCost}
        >
          Request Ride
        </Button>
      )}
    </XView>
  );
}
