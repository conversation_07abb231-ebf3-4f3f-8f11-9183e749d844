import { useRouter } from "expo-router";
import { useAuth } from "@x/auth";
import { useCallback, useEffect, useState } from "react";
import { io, Socket } from "socket.io-client";
import { WS_URL } from "@x/constants";
import { Coordinates } from "@x/rn-shared";

export default function useRideSocket(
  rideId: string,
  options: {
    onPickedUp: () => void;
    onArrived: () => void;
    onDroppedOff: () => void;
  },
) {
  const router = useRouter();
  const { user } = useAuth();
  const [socket, setSocket] = useState<Socket | null>(null);
  const [token, setToken] = useState<string | undefined>();
  const [driverLocation, setDriverLocation] = useState<Coordinates | null>(
    null,
  );

  useEffect(() => {
    const getToken = async () => {
      const token = await user?.getIdToken(true);
      setToken(token);
    };
    void getToken();
  }, []);

  const handleMessage = useCallback((message: string) => {}, [router, rideId]);

  useEffect(() => {
    if (!token) return;

    const socket = io(`${WS_URL}/ride?rideId=${rideId}`, {
      extraHeaders: {
        Authorization: `Bearer ${token}`,
      },
      reconnectionDelayMax: 1000,
    });

    const connection = socket.connect();
    setSocket(connection);

    socket.on("connect", () => {
      console.log("Connected to server");
    });

    socket.on("disconnect", () => {
      console.log("Disconnected from server");
    });

    socket.on("error", (error) => {
      console.error("Error from server", error);
    });

    socket.on("message", (message) => {
      handleMessage(message);
      console.log("Message from server", message);
    });

    socket.on("location", (location: Coordinates) => {
      setDriverLocation(location);
    });

    socket.on("data", (data) => {
      if (data === "CONFIRM") {
        options.onPickedUp();
      }
      if (data === "READY_TO_PICKUP") {
        options.onArrived();
      }
      if (data === "CONFIRM_DROPOFF") {
        options.onDroppedOff();
      }
    });

    socket.onAny((event, ...args) => {
      console.log(event, args);
    });

    return () => {
      connection.disconnect();
    };
  }, [token, handleMessage]);

  return {
    driverLocation,
  };
}
