import { Switch, Text } from "react-native-paper";
import React, { useEffect, useState } from "react";
import { useSuspenseQuery } from "@tanstack/react-query";
import { queries, useUpdateWorkingStatus } from "@x/api";
import { XView } from "@x/rn-shared";

export default function WorkingStatusBar() {
  const { data: driver } = useSuspenseQuery(queries.drivers.me);
  if (!driver) throw new Error("No driver data.");

  const { mutate: setWorkingStatus } = useUpdateWorkingStatus(driver.id);

  const isOnline = driver.workingStatus === "ONLINE";
  const [dots, setDots] = useState("");

  useEffect(() => {
    if (isOnline) {
      const interval = setInterval(() => {
        setDots((prev) => (prev.length < 3 ? prev + "." : ""));
      }, 500);

      return () => clearInterval(interval);
    }
  }, [isOnline]);

  return (
    <XView
      style={{
        flexDirection: "row",
        justifyContent: "space-between",
        paddingVertical: 24,
        paddingHorizontal: 32,
        borderTopStartRadius: 16,
        borderTopEndRadius: 16,
      }}
    >
      <Text variant="titleLarge">
        {isOnline ? `Finding the trip${dots}` : "You're Offline!"}
      </Text>
      <Switch
        value={isOnline}
        onValueChange={(status) => {
          setWorkingStatus(status ? "ONLINE" : "OFFLINE");
        }}
      />
    </XView>
  );
}
