import { Appbar } from "react-native-paper";
import { Image, TouchableOpacity, View } from "react-native";
import { useAuth } from "@x/auth";

export function AppBar() {
  const { user, logout } = useAuth();

  return (
    <Appbar.Header mode="center-aligned" style={{ paddingLeft: 16 }}>
      {/*<Appbar.BackAction onPress={() => {}} />*/}
      <TouchableOpacity onPress={logout}>
        <Image
          source={{ uri: user?.photoURL ?? "https://via.placeholder.com/150" }}
          style={{
            width: 40,
            height: 40,
            borderRadius: 8,
          }}
        />
      </TouchableOpacity>
      <Appbar.Content
        mode="center-aligned"
        style={{ height: 64, position: "static" }}
        title={
          <View
            style={{
              position: "absolute",
              left: 0,
              right: 0,
              alignItems: "center",
            }}
          >
            <Image
              source={require("@x/rn-shared/assets/images/logo.png")}
              resizeMode="contain"
              style={{
                aspectRatio: 1,
                height: 60,
              }}
            />
          </View>
        }
      />
      <Appbar.Action icon="bell" />
    </Appbar.Header>
  );
}
