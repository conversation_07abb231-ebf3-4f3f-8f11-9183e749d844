import { Animated, Image, View } from "react-native";
import { Button, Text, useTheme } from "react-native-paper";
import React, { useEffect, useRef, useState } from "react";
import { useRouter } from "expo-router";
import useAcceptRide from "@x/api/src/mutations/rides/use-accept-ride";
import { useSuspenseQuery } from "@tanstack/react-query";
import { queries } from "@x/api";

type TripRequestType = "regular" | "garage" | "mechanic";

const tripTypes: TripRequestType[] = ["regular", "garage", "mechanic"]; // List of trip types

const tripRequest = {
  driverName: "Cody Fisher",
  driverRating: 4.8,
  driverImage: "https://via.placeholder.com/150",
  fare: 500,
  distance: "2 km",
  timeAway: "20 min",
  pickupPoint: "6364 Elgin Delaware 10823",
  destinationPoint: "8502 Preston Rd. Inglewood, Maine 98380",
  pickupCoordinates: { latitude: 31.5204, longitude: 74.3587 },
  destinationCoordinates: { latitude: 31.5497, longitude: 74.3436 },
  tripType: "regular" as TripRequestType,
};

interface TripRequestPopupProps {
  rideId: string;
  onDismiss: () => void;
}

export default function TripRequestPopup({
  rideId,
  onDismiss,
}: TripRequestPopupProps) {
  const router = useRouter();
  const cardAnimation = useRef(new Animated.Value(0)).current;
  const theme = useTheme();
  // const router = useRouter();
  const { mutateAsync: acceptRide } = useAcceptRide();

  useEffect(() => {
    Animated.timing(cardAnimation, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  function handleDismissTrip() {
    onDismiss();
  }

  async function handleAcceptTrip() {
    // router.push("/offer-details/123");
    await acceptRide(rideId);
    router.replace({
      pathname: "/ride/[rideId]",
      params: {
        rideId: rideId,
      },
    });
  }

  const { data: rideDetails } = useSuspenseQuery(queries.rides.detail(rideId));

  const [timer, setTimer] = useState(60);

  useEffect(() => {
    const interval = setInterval(() => {
      setTimer((prev) => {
        if (prev === 0) {
          clearInterval(interval);
          handleDismissTrip();
          return 0;
        }

        return prev - 1;
      });
    }, 1000);

    return () => {
      clearInterval(interval);
    };
  }, []);

  return (
    <Animated.View
      style={[
        {
          position: "absolute",
          zIndex: 10,
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: theme.colors.primaryContainer,
          padding: 16,
          borderTopStartRadius: 10,
          borderTopEndRadius: 10,
          gap: 24,
        },
        {
          transform: [
            {
              translateY: cardAnimation.interpolate({
                inputRange: [0, 1],
                outputRange: [500, 0],
              }),
            },
          ],
        },
      ]}
    >
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
        }}
      >
        <Image
          source={{ uri: tripRequest.driverImage }}
          style={{
            width: 50,
            height: 50,
            borderRadius: 100,
            marginRight: 10,
          }}
        />
        <View
          style={{
            flex: 1,
          }}
        >
          <Text variant="titleMedium">{tripRequest.driverName}</Text>
          <Text variant="bodySmall">
            {rideDetails.distanceInMeters / 1000} km -{" "}
            {rideDetails.durationInSeconds / 60} min away
          </Text>
        </View>
        <Text variant="labelLarge">
          {rideDetails.currency} {rideDetails.totalFare}
        </Text>
      </View>
      <View
        style={{
          gap: 8,
        }}
      >
        <View>
          <Text variant="labelLarge">Pickup Point:</Text>
          <Text variant="bodyMedium">{rideDetails.origin}</Text>
        </View>
        {rideDetails.tripType === "TOWING" && (
          <View>
            <Text variant="labelLarge">Destination Point:</Text>
            <Text variant="bodyMedium">{rideDetails.destination}</Text>
          </View>
        )}
      </View>
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          gap: 8,
          borderTopColor: "#ccc",
          borderTopWidth: 1,
          paddingTop: 16,
        }}
      >
        <Text
          variant="headlineMedium"
          style={{
            flex: 1,
          }}
        >{`00:${timer < 10 ? `0${timer}` : timer}`}</Text>
        <Button onPress={handleDismissTrip} mode="outlined" icon="cancel">
          Dismiss
        </Button>
        <Button
          onPress={handleAcceptTrip}
          mode="contained"
          icon="arrow-right"
          contentStyle={{
            flexDirection: "row-reverse",
          }}
        >
          Accept Ride
        </Button>
      </View>
    </Animated.View>
  );
}
