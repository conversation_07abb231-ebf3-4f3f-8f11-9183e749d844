import { useRouter } from "expo-router";
import { useCallback, useEffect, useState } from "react";
import { io, Socket } from "socket.io-client";
import { useInterval } from "usehooks-ts";
import * as Location from "expo-location";
import { locationOptions } from "@/features/location/use-location-tracker";
import { useAuth } from "@x/auth";
import { WS_URL } from "@x/constants";

enum RideStatus {
  CONFIRM = "CONFIRM",
  READY_TO_PICKUP = "READY_TO_PICKUP",
  CONFIRM_DROPOFF = "CONFIRM_DROPOFF",
}

export default function useRideSocket(rideId: string) {
  const router = useRouter();
  const { user } = useAuth();
  const [socket, setSocket] = useState<Socket | null>(null);
  const [token, setToken] = useState<string | undefined>();

  useEffect(() => {
    const getToken = async () => {
      const token = await user?.getIdToken(true);
      setToken(token);
    };

    void getToken();
  }, [rideId]);

  const handleMessage = useCallback((message: string) => {}, [router, rideId]);

  useEffect(() => {
    if (!token) return;

    const socket = io(`${WS_URL}/ride?rideId=${rideId}`, {
      extraHeaders: {
        Authorization: `Bearer ${token}`,
      },
    });

    const connection = socket.connect();
    setSocket(connection);

    socket.on("connect", () => {
      console.log("Connected to server");
    });

    socket.on("disconnect", () => {
      console.log("Disconnected from server");
    });

    socket.on("error", (error) => {
      console.error("Error from server", error);
    });

    socket.on("message", (message) => {
      handleMessage(message);
      console.log("Message from server", message);
    });

    socket.onAny((event, ...args) => {
      console.log(event, args);
    });

    return () => {
      connection.disconnect();
    };
  }, [token, handleMessage]);

  // Fetch and send location at intervals
  useInterval(() => {
    Location.getCurrentPositionAsync(locationOptions).then((location) => {
      const { latitude, longitude } = location.coords;

      if (!socket) return;

      socket.emit("liveLocation", {
        latitude,
        longitude,
      });
    });
  }, 1000);

  function onReached() {
    socket?.emit("message", RideStatus.READY_TO_PICKUP);
  }

  function onPicked() {
    socket?.emit("message", RideStatus.CONFIRM);
  }

  function onDropped() {
    socket?.emit("message", RideStatus.CONFIRM_DROPOFF);
  }

  return {
    onReached,
    onPicked,
    onDropped,
  };
}
