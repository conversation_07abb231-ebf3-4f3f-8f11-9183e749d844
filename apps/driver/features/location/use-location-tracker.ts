import * as Location from "expo-location";
import { LocationOptions } from "expo-location";
import { useEffect, useState } from "react";
import { useInterval } from "usehooks-ts";
import { io, Socket } from "socket.io-client";
import { useAuth } from "@x/auth";
import { useSuspenseQuery } from "@tanstack/react-query";
import { queries } from "@x/api";
import { WS_URL } from "@x/constants";

export const locationOptions: LocationOptions = {
  accuracy: Location.Accuracy.Highest,
  timeInterval: 1000,
  distanceInterval: 1,
};

type LocationCoords = {
  latitude: number;
  longitude: number;
};

export function useLocationTracker(props: {
  onRideRequest: (rideId: string) => void;
}) {
  const { data: driver } = useSuspenseQuery(queries.drivers.me);
  if (!driver) throw new Error("No driver data.");

  const { user } = useAuth();
  const [fgStatus, requestFGPermission] = Location.useForegroundPermissions();
  const [location, setLocation] = useState<LocationCoords | null>(null);
  const [socket, setSocket] = useState<Socket | null>(null);
  const [token, setToken] = useState<string | undefined>();

  useEffect(() => {
    const getToken = async () => {
      const token = await user?.getIdToken();
      setToken(token);
    };
    void getToken();
  }, []);

  // Request foreground permissions
  useEffect(() => {
    (async () => {
      if (!fgStatus?.granted) {
        const { status } = await requestFGPermission();
        if (status !== "granted") {
          console.error("Permission to access location was denied");
          return;
        }
      }
    })();
  }, [fgStatus]);

  function handleRideRequest(rideId: string | undefined) {
    if (!rideId) return;
    // if (rideId.startsWith("ride:")) {
    //   rideId = rideId.slice(5);
    // }
    props.onRideRequest(rideId);
  }

  // WebSocket connection management
  useEffect(() => {
    if (driver.workingStatus === "ONLINE") {
      const socket = io(`${WS_URL}/driver`, {
        extraHeaders: {
          Authorization: `Bearer ${token}`,
        },
      });
      const connection = socket.connect();
      setSocket(connection);

      socket.on("connect", () => {
        console.log("Connected to server");
      });

      socket.on("disconnect", () => {
        console.log("Disconnected from server");
      });

      socket.on("error", (error) => {
        console.error("Error from server", error);
      });

      socket.onAny((event, ...args) => {
        console.log(event, args);
      });

      socket.on("RideRequest", (rideId) => {
        handleRideRequest(rideId);
      });

      return () => {
        connection.disconnect();
      };
    }
  }, [driver.workingStatus, token]);

  // Fetch and send location at intervals
  useInterval(
    () => {
      Location.getCurrentPositionAsync(locationOptions).then((location) => {
        const { latitude, longitude } = location.coords;

        setLocation({ latitude, longitude });

        if (!socket) return;

        socket.emit("driverLiveLocation", {
          latitude,
          longitude,
          truckType: "STANDARD",
        });

        // console.log("Sent location to server", { latitude, longitude });
      });
    },
    driver.workingStatus === "ONLINE" && fgStatus?.granted && socket
      ? 1000
      : null, // Every 1000ms
  );

  return location;
}
