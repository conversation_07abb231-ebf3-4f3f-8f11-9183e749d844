{"expo": {"name": "Towing <PERSON>", "slug": "tg-driver", "version": "1.0.0", "owner": "towing_guy", "orientation": "portrait", "icon": "./assets/branding/ios-light.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "platforms": ["android", "ios"], "ios": {"icon": {"dark": "./assets/branding/ios-dark.png", "light": "./assets/branding/ios-light.png", "tinted": "./assets/branding/ios-tinted.png"}, "googleServicesFile": "./features/firebase/GoogleService-Info.plist", "bundleIdentifier": "com.towingguy.driverapp", "supportsTablet": true}, "android": {"package": "com.towingguy.driverapp", "adaptiveIcon": {"foregroundImage": "./assets/branding/adaptive-icon.png", "backgroundColor": "#FFFFFF"}, "config": {"googleMaps": {"apiKey": "AIzaSyAsWkaTz3LVcWUnxjMj_-oTqUwMvvXzOGI"}}, "googleServicesFile": "./features/firebase/google-services.json", "permissions": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_BACKGROUND_LOCATION", "android.permission.FOREGROUND_SERVICE", "android.permission.FOREGROUND_SERVICE_LOCATION"]}, "plugins": ["expo-router", "@react-native-firebase/app", "@react-native-firebase/auth", "@react-native-google-signin/google-signin", ["expo-splash-screen", {"image": "./assets/branding/splash-icon-dark.png", "backgroundColor": "#FFFFFF", "dark": {"image": "./assets/branding/splash-icon-light.png", "backgroundColor": "#000000"}, "resizeMode": "contain", "imageWidth": 200}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location.", "isAndroidBackgroundLocationEnabled": true, "isIosBackgroundLocationEnabled": true}]], "experiments": {"typedRoutes": true, "reactCanary": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "441d04fc-e817-454d-bf9a-958c8bb83cae"}}}}