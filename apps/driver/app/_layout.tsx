import {
  MD3DarkTheme as <PERSON><PERSON><PERSON><PERSON>,
  MD3LightTheme as <PERSON><PERSON><PERSON><PERSON>hem<PERSON>,
  PaperProvider,
} from "react-native-paper";
import { useFonts } from "expo-font";
import * as SplashScreen from "expo-splash-screen";
import { StatusBar } from "expo-status-bar";
import { useEffect } from "react";
import "react-native-reanimated";
import { ActivityIndicator, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { ApiProvider } from "@x/api";
import { AuthProvider, useAuth } from "@x/auth";
import { GoogleSignin } from "@react-native-google-signin/google-signin"; // Prevent the splash screen from auto-hiding before asset loading is complete.
import { Slot } from "expo-router";
import { getIdToken } from "@react-native-firebase/auth";

const theme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: "black",
    primaryContainer: "white",
  },
  roundness: 2,
};

const darkTheme = {
  ...DarkTheme,
  colors: {
    ...DarkTheme.colors,
    primary: "white",
  },
  roundness: 2,
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
void SplashScreen.preventAutoHideAsync();

GoogleSignin.configure({
  webClientId:
    "656013131691-pn21b9d5svs5p1v8biuea1k93d6td4av.apps.googleusercontent.com",
});

export default function RootLayout() {
  // TODO: fix color scheme
  const colorScheme: string = "light";
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  });

  useEffect(() => {
    if (loaded) {
      void SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return (
      <SafeAreaView
        style={{ flex: 1, justifyContent: "center", alignItems: "center" }}
      >
        <ActivityIndicator size="large" />
      </SafeAreaView>
    );
  }

  return (
    <PaperProvider theme={colorScheme === "dark" ? darkTheme : theme}>
      <AuthProvider>
        <_RootLayout colorScheme={colorScheme} />
      </AuthProvider>
    </PaperProvider>
  );
}

function _RootLayout(props: { colorScheme: string }) {
  const { user } = useAuth();

  return (
    <ApiProvider fetchToken={() => (user ? getIdToken(user) : undefined)}>
      <StatusBar style="dark" />
      <View
        style={{
          flex: 1,
          backgroundColor:
            props.colorScheme === "dark"
              ? darkTheme.colors.background
              : theme.colors.background,
        }}
      >
        <Slot />
      </View>
    </ApiProvider>
  );
}
