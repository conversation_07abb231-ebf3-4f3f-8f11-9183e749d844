import { StyleSheet, TouchableOpacity, View } from "react-native";
import { Text } from "react-native-paper";
import { FontAwesome } from "@expo/vector-icons";
import { useAuth } from "@x/auth";

export default function InactiveDriver() {
  const { logout } = useAuth();

  return (
    <View style={styles.container}>
      {/* Error Icon */}
      <View style={styles.iconContainer}>
        <FontAwesome name="user" size={100} color="red" />
        <View style={styles.crossIcon}>
          <FontAwesome name="times-circle" size={30} color="red" />
        </View>
      </View>

      {/* Error Message */}
      <Text style={styles.errorMessage}>Driver account is not active</Text>

      {/*// TODO: fix the message*/}

      {/* Logout Button */}
      <TouchableOpacity onPress={logout} style={styles.logoutButton}>
        <Text style={styles.logoutButtonText}>Logout</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
  },
  iconContainer: {
    position: "relative",
    alignItems: "center",
    marginBottom: 20,
  },
  crossIcon: {
    position: "absolute",
    right: -20,
    bottom: 10,
    backgroundColor: "#fff",
    borderRadius: 15,
    padding: 2,
  },
  errorMessage: {
    color: "#ff0000",
    backgroundColor: "#ffcccc",
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 10,
    textAlign: "center",
    fontSize: 14,
    marginBottom: 80,
  },
  logoutButton: {
    backgroundColor: "#ff0000",
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 8,
    top: 60,
  },
  logoutButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "bold",
  },
});
