import { Redirect, Slot } from "expo-router";
import { View } from "react-native";
import { useAuth } from "@x/auth";
import { AppBar } from "@/features/common/components/app-bar";

export default function AuthenticatedLayout() {
  const { user } = useAuth();
  if (!user) {
    return <Redirect href="/login" />;
  }

  return (
    <View
      style={{
        flex: 1,
      }}
    >
      <AppBar />
      <Slot />
    </View>
  );
}
