import { Redirect, Slot } from "expo-router";
import { useSuspenseQuery } from "@tanstack/react-query";
import { queries } from "@x/api";

export default function DriverLayout() {
  const { data: userProfile } = useSuspenseQuery(queries.users.me);
  if (!userProfile) throw new Error("User profile not found");

  const { data: driverProfile, isError } = useSuspenseQuery(queries.drivers.me);

  if (!userProfile.roles.includes("DRIVER") || isError || !driverProfile) {
    return <Redirect href="/not-a-driver" />;
  }

  if (!isError && driverProfile?.status !== "ACTIVE") {
    return <Redirect href="/inactive-driver" />;
  }

  return <Slot />;
}
