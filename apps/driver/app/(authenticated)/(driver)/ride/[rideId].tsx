import { useLocalSearchParams, useRouter } from "expo-router";
import useRideSocket from "@/features/rides/hooks/use-ride-socket";
import { View } from "react-native";
import MapView, { Marker } from "react-native-maps";
import { Button } from "react-native-paper";
import { useEffect, useRef, useState } from "react";
import { parseCoords, useCurrentLocation } from "@x/rn-shared";
import { useSuspenseQuery } from "@tanstack/react-query";
import { queries } from "@x/api";

export default function RidePage() {
  const { rideId } = useLocalSearchParams<{
    rideId: string;
  }>();

  const router = useRouter();

  const { data: currentLocation } = useCurrentLocation();
  const [rideState, setRideState] = useState<
    "towardsPickup" | "pickingUp" | "towardsDestination"
  >("towardsPickup");

  const { onReached, onPicked, onDropped } = useRideSocket(rideId);

  const { data: rideDetails } = useSuspenseQuery(queries.rides.detail(rideId));
  const mapRef = useRef<MapView>(null);
  const origin = parseCoords(rideDetails.origin);
  const destination = parseCoords(rideDetails.destination);

  function handleNextAction() {
    if (rideState === "towardsPickup") {
      setRideState("pickingUp");
      if (rideDetails.tripType === "MECHANIC") {
        router.replace("/");
      }
      onReached();
    } else if (rideState === "pickingUp") {
      setRideState("towardsDestination");
      onPicked();
    } else {
      onDropped();
      router.replace("/");
    }
  }

  useEffect(() => {
    if (rideState === "towardsPickup") {
      mapRef.current?.fitToCoordinates(
        [origin ?? currentLocation, currentLocation],
        {
          edgePadding: { top: 100, right: 100, bottom: 100, left: 100 },
        },
      );
    } else if (rideState === "pickingUp") {
      mapRef.current?.fitToCoordinates(
        [origin ?? currentLocation, currentLocation],
        {
          edgePadding: { top: 100, right: 100, bottom: 100, left: 100 },
        },
      );
    } else {
      mapRef.current?.fitToCoordinates(
        [destination ?? currentLocation, currentLocation],
        {
          edgePadding: { top: 100, right: 100, bottom: 100, left: 100 },
        },
      );
    }
  }, [rideState, origin, destination, currentLocation]);

  return (
    <View style={{ flex: 1 }}>
      <View style={{ flex: 1 }}>
        <MapView
          ref={mapRef}
          initialRegion={{
            latitude: currentLocation.latitude,
            longitude: currentLocation.longitude,
            latitudeDelta: 0.005,
            longitudeDelta: 0.005,
          }}
          followsUserLocation
          showsUserLocation
          style={{ width: "100%", height: "100%" }}
        >
          {origin && <Marker coordinate={origin} />}
          {destination && <Marker coordinate={destination} pinColor="green" />}
        </MapView>
      </View>
      <View
        style={{
          backgroundColor: "white",
          paddingVertical: 16,
          paddingHorizontal: 32,
        }}
      >
        <Button mode="contained" onPress={handleNextAction}>
          {
            {
              towardsPickup: "Reached Location",
              pickingUp: "Picked Up",
              towardsDestination: "Dropped",
            }[rideState]
          }
        </Button>
      </View>
    </View>
  );
}
