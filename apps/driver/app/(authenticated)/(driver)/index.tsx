import React, { useRef, useState } from "react";
import { StyleSheet, View } from "react-native";
import { Text } from "react-native-paper";
import MapView, { PROVIDER_GOOGLE } from "react-native-maps";
import { useLocationTracker } from "@/features/location/use-location-tracker";
import WorkingStatusBar from "@/features/common/components/working-status-bar";
import TripRequestPopup from "@/features/common/components/trip-request-popup";
import { useCurrentLocation } from "@x/rn-shared";

export default function DriverHome() {
  const [rideRequest, setRideRequest] = useState<string | undefined>(undefined);

  useLocationTracker({
    onRideRequest: (rid) => {
      setRideRequest(rid);
    },
  });

  const { data: currentLocation } = useCurrentLocation();

  const mapRef = useRef<MapView>(null);

  return (
    <View style={styles.container}>
      <View style={styles.mapContainer}>
        <MapView
          provider={PROVIDER_GOOGLE}
          ref={mapRef}
          style={{ width: "100%", height: "100%" }}
          initialRegion={{
            latitude: currentLocation.latitude,
            longitude: currentLocation.longitude,
            latitudeDelta: 0.005,
            longitudeDelta: 0.005,
          }}
          showsUserLocation
          followsUserLocation
        ></MapView>

        <View style={styles.pkrContainer}>
          <Text style={styles.headerBalance}>PKR 0.00</Text>
        </View>
      </View>

      <WorkingStatusBar />

      {rideRequest && (
        <TripRequestPopup
          rideId={rideRequest}
          onDismiss={() => setRideRequest(undefined)}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    height: 60,
  },
  headerProfileImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  headerLogo: {
    width: 80,
    height: 40,
    resizeMode: "contain",
  },
  bellIcon: {
    marginRight: 0,
  },
  mapContainer: {
    flex: 1,
  },
  pkrContainer: {
    position: "absolute",
    top: 1,
    alignSelf: "center",
    padding: 8,
  },
  headerBalance: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#fff",
    padding: 8,
    borderRadius: 12,
    backgroundColor: "#000",
  },
  loaderContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  offlineContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderTopWidth: 1,
    borderColor: "#ddd",
    elevation: 5,
  },
});
