import { View } from "react-native";
import { Button, Text } from "react-native-paper";
import { useAuth } from "@x/auth";

export default function NotADriver() {
  const { logout } = useAuth();

  return (
    <View
      style={{
        flex: 1,
        justifyContent: "center",
        alignItems: "center",
        padding: 32,
        gap: 8,
      }}
    >
      <Text variant="headlineSmall" style={{ textAlign: "center" }}>
        You are not a registered driver
      </Text>
      <Text
        style={{
          maxWidth: 300,
        }}
      >
        Please contact support if you need any help.
      </Text>
      <Button
        style={{ marginTop: 32 }}
        mode="outlined"
        onPress={logout}
        icon="logout"
      >
        Logout
      </Button>
    </View>
  );
}
