import { Redirect, Slot } from "expo-router";
import { useEffect } from "react";
import { useAuth } from "@x/auth";

export default function UnAuthenticatedLayout() {
  const { user } = useAuth();
  if (user) {
    return <Redirect href="/" />;
  }

  // const { isVerified } = useAuth();
  // const navigation = useNavigation();

  useEffect(() => {
    // TODO: verify if this is working, implement it properly maybe in the authenticated layout?
    // if (!isVerified()) {
    //   // If email or phone is not verified, redirect to the verification screen
    //   navigation.navigate("/verify-mobile-screen");
    // }
  }, []);

  return <Slot />;
}
