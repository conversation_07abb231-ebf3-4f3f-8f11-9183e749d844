import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@heroui/react";

interface ConfirmationDialogProps {
  isOpen: boolean;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
}

export default function ConfirmationDialog({ isOpen, message, onConfirm, onCancel }: ConfirmationDialogProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onCancel}>
      <DialogContent className="p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <DialogHeader>
          <DialogTitle className="text-lg font-semibold text-gray-900 dark:text-white">
            Confirmation
          </DialogTitle>
        </DialogHeader>
        <p className="text-gray-700 dark:text-gray-300">{message}</p>
        <DialogFooter className="flex justify-end space-x-2 mt-4">
          <Button onPress={onCance<PERSON>} className="bg-gray-500 text-white">No</Button>
          <Button onPress={onConfirm} className="bg-blue-600 text-white">Yes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
