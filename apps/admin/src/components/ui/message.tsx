import React, { useEffect, useState } from "react";

interface ToastProps {
  message: string;
  textColor?: string;
  bgColor?: string;
  duration?: number;
  onClose: () => void;
}

const Toast: React.FC<ToastProps> = ({
  message,
  textColor = "text-white",
  bgColor = "bg-green-500",
  duration = 3000,
  onClose,
}) => {
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    // Show the toast initially by setting visible to true
    setVisible(true);

    // Auto-hide the toast after the duration and slide out
    const timer = setTimeout(() => {
      setVisible(false);
      setTimeout(onClose, 500); // Wait for slide-out transition to complete before closing
    }, duration);

    // Cleanup the timer
    return () => clearTimeout(timer);
  }, [onClose, duration]);

  return (
    <div
      className={`fixed top-4 right-4 z-50 p-4 rounded shadow-lg transition-transform duration-500 ease-in-out ${
        visible ? "translate-x-0 opacity-100" : "translate-x-full opacity-0"
      } ${bgColor} ${textColor}`}
    >
      {message}
    </div>
  );
};

export default Toast;
