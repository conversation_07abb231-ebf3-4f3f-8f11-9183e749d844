import { createFileRoute, Outlet, redirect } from "@tanstack/react-router";

export const Route = createFileRoute("/_auth")({
  component: Outlet,
  beforeLoad: ({ context }) => {
    // TODO: check if the user is authenticated, redirect it to the _dashboard
    if (context.user) {
      console.log("Authenticated Context _auth : ", context);
      throw redirect({ to: "/" });
    }
  },
});

// function AuthLayout() {
//   return <Outlet />;
// }
