import {
  createFileRoute,
  Link,
  Outlet,
  redirect,
  useNavigate,
} from "@tanstack/react-router";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import {
  ChevronRightIcon,
  CompassIcon,
  HomeIcon,
  MapPin,
  MenuIcon,
  Moon,
  SettingsIcon,
  ShipWheelIcon,
  Sun,
  Truck,
  UsersIcon,
} from "lucide-react";
import { useTheme } from "@/components/ThemeProvider.tsx";
import { useAuth } from "@/auth/use-auth.ts";
import useUserProfile from "@/utils/hooks/use-current-loggedIn-user.ts";
import { cn } from "@/lib/utils.ts";
import { Separator } from "@/components/ui/separator";
import { Role } from "@/utils/constant.ts";
import { Image } from "@heroui/react";

export const Route = createFileRoute("/_authenticated")({
  component: Navigation,
  beforeLoad: ({ context }) => {
    if (!context.user) {
      throw redirect({ to: "/login" });
    }
  },
});

function Navigation() {
  const navigate = useNavigate();
  const { setTheme, theme } = useTheme();
  const { logout } = useAuth();
  const toggleTheme = () => {
    const newTheme = theme === "light" ? "dark" : "light";
    setTheme(newTheme);
  };

  const logo = "/Logo.png";
  const Mobilelogo = theme === "light" ? "/Logo-forLight.png" : "/Logo.png";

  const user = useUserProfile();
  if (user) {
    if (!user.data.roles.includes(Role.ADMIN)) {
      logout();
    }
  } else {
    logout();
  }

  const navigationItems = [
    {
      name: "Dashboard",
      path: "/",
      icon: HomeIcon,
    },
    {
      name: "Drivers",
      icon: ShipWheelIcon,
      children: [
        {
          name: "All Drivers",
          path: "/drivers",
        },
        {
          name: "Driver Registration",
          path: "/drivers/register-driver",
        },
      ],
    },

    {
      name: "Trucks",
      icon: Truck,
      children: [
        {
          name: "All Trucks",
          path: "/trucks",
        },
        {
          name: "Truck Registration",
          path: "/trucks/register-truck",
        },
      ],
    },
    {
      name: "Users",
      icon: UsersIcon,
      children: [
        {
          name: "All Users",
          path: "/users",
        },
      ],
    },
    {
      name: "Trips",
      icon: CompassIcon,
      children: [
        {
          name: "All Trips",
          path: "/trips",
        },
      ],
    },
    {
      name: "Locations",
      icon: MapPin,
      children: [
        {
          name: "All Locations",
          path: "/locations",
        },
        {
          name: "Locations Registration",
          path: "/locations/register-location",
        },
      ],
    },
    {
      name: "Platform Settings",
      icon: SettingsIcon,
      children: [
        {
          name: "Destination Charges",
          path: "/settings/destination-charges",
        },
        {
          name: "Illegal parking Charges",
          path: "/settings/illegal-parking-charges",
        },
        {
          name: "Road Assistance Charges",
          path: "/settings/road-assistance-charges",
        },
      ],
    },
  ];

  const getPageTitle = (pathname: string) => {
    for (const item of navigationItems) {
      if (item.path === pathname) {
        return item.name;
      }
      if (item.children) {
        const child = item.children.find((child) => child.path === pathname);
        if (child) {
          return child.name;
        }
      }
    }
    return "Dashboard"; // Default title if no match is found
  };

  const currentTitle: string = getPageTitle(location.pathname);

  return (
    <div className="flex min-h-screen w-full flex-col bg-slate-50 dark:bg-slate-900 ">
      <aside className="text-white hidden fixed inset-y-0 left-0 z-10 w-64 sm:flex flex-col border-r bg-slate-900 dark:bg-slate-950 overflow-y-auto max-h-screen scrollbar-thin scrollbar-track-slate-300 dark:scrollbar-track-slate-700 scrollbar-thumb-slate-500">
        <div className="flex h-14 items-center border-b px-4 py-4">
          <Image src={logo} className={"h-10 w-full"} alt="logo" />
          <Link to="/" className="flex ml-4 items-center gap-2 font-semibold">
            <span className="font-bold text-xl">Towing Guy</span>
          </Link>
        </div>
        <nav className="grid gap-4 px-4 py-6 text-sm font-medium">
          {navigationItems.map((item) => (
            <div key={item.name}>
              {item.children ? (
                <Collapsible className="grid gap-4">
                  <CollapsibleTrigger className="flex w-full items-center text-lg font-semibold [&[data-state=open]>svg#arrow]:rotate-90">
                    <item.icon className="h-4 w-4 mr-2" />
                    {item.name}
                    <ChevronRightIcon
                      id="arrow"
                      className="ml-auto h-5 w-5 duration-200"
                    />
                  </CollapsibleTrigger>
                  <CollapsibleContent className="transition-all duration-500 ease-in-out max-h-0 overflow-hidden rounded-lg data-[state=open]:max-h-[500px]">
                    <div className="grid gap-1 p-2 bg-slate-700 dark:bg-slate-900 rounded-lg animate-fade-right">
                      {item.children.map((child) => (
                        <Link
                          key={child.name}
                          to={child.path}
                          className="group grid h-auto w-full justify-start gap-1 items-center rounded-lg py-2 px-4 hover:bg-slate-600 transition-all"
                        >
                          <div className="text-sm font-medium">
                            {child.name}
                          </div>
                        </Link>
                      ))}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              ) : (
                <Link
                  to={item.path}
                  className="flex items-center gap-3 rounded-lg px-3 py-2 hover:bg-slate-700 dark:hover:bg-slate-700 transition-all"
                >
                  <item.icon className="h-4 w-4" />
                  {item.name}
                </Link>
              )}
            </div>
          ))}
        </nav>
      </aside>

      {/*Sheets for mobile screen */}
      <div className="flex flex-1 scroll flex-col sm:pl-60">
        <header className="flex py-3 items-center gap-4 border-b bg-background px-4  sm:static sm:h-auto sm:w-full sm:border-0 sm:bg-transparent sm:px-6">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon" className="sm:hidden">
                <MenuIcon />
                <span className="sr-only">Toggle navigation</span>
              </Button>
            </SheetTrigger>
            <SheetContent
              side="left"
              className="sm:max-w-xs sm:max-h-screen overflow-y-auto"
            >
              <Image
                src={Mobilelogo}
                className={"-translate-y-3 -translate-x-3 h-8 w-full"}
                alt="logo"
              />
              <nav className="grid gap-4 px-4 py-6 text-sm font-medium">
                {navigationItems.map((item) =>
                  item.children ? (
                    <Collapsible key={item.name} className="grid gap-4">
                      <CollapsibleTrigger className="flex w-full items-center text-lg font-semibold [&[data-state=open]>svg]:rotate-90">
                        <item.icon className="h-4 w-4 mr-2" />
                        {item.name}
                        <ChevronRightIcon className="ml-auto h-5 w-5 transition-all" />
                      </CollapsibleTrigger>
                      <CollapsibleContent>
                        <div className="-mx-6 grid gap-6 bg-muted p-6">
                          {item.children.map((child) => (
                            <Link
                              key={child.name}
                              to={child.path}
                              className="group grid h-auto w-full justify-start gap-1"
                            >
                              {({ isActive }) => (
                                <div
                                  className={cn(
                                    "text-sm font-medium leading-none group-hover:underline",
                                    isActive && "font-black text-red-500",
                                  )}
                                >
                                  {child.name}
                                </div>
                              )}
                            </Link>
                          ))}
                        </div>
                      </CollapsibleContent>
                    </Collapsible>
                  ) : (
                    <Link
                      key={item.name}
                      to={item.path}
                      className="flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-primary"
                    >
                      <item.icon className="h-4 w-4" />
                      {item.name}
                    </Link>
                  ),
                )}
              </nav>
            </SheetContent>
          </Sheet>
          <div className="flex w-full items-center">
            <h1 className="text-2xl ml-6 font-bold text-center ">
              {currentTitle}
            </h1>
            {/*add the search bar here */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="rounded-full border w-8 h-8 ml-auto"
                >
                  <img
                    src={user.data?.profilePic || "/avatar.png"}
                    width="32"
                    height="32"
                    className="rounded-full"
                    alt="Avatar"
                    style={{ aspectRatio: "32/32", objectFit: "cover" }}
                  />
                  <span className="sr-only">Toggle user menu</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => navigate({ to: "/settings/profile" })}
                >
                  View Profile
                </DropdownMenuItem>
                <DropdownMenuItem className={"hidden"}>
                  Settings
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={toggleTheme}>
                  {theme === "light" ? (
                    <>
                      <Moon /> Dark Mode
                    </>
                  ) : (
                    <>
                      <Sun /> Light Mode
                    </>
                  )}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={logout}>Logout</DropdownMenuItem>
                <DropdownMenuSeparator />
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </header>
        <Separator />
        <div className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-6 bg-inherit">
          <Outlet />
        </div>
      </div>
    </div>
  );
}
