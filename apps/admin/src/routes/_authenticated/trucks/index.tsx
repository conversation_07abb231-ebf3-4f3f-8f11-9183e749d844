import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import useGetAllRegisteredTrucks from "@/utils/hooks/truck/get-all-registered-trucks.ts";
import { components } from "@/api-schema"; // Import the types
import { SlidersHorizontal } from "lucide-react";
import { TruckDialog } from "@/routes/_authenticated/trucks/-components/truckmodal.tsx";
import { toast, ToastContainer } from "react-toastify"; // Import toast and ToastContainer
import "react-toastify/dist/ReactToastify.css"; // Import toast styles
import PagePagination from "@/components/PagePagination.tsx"; // Import the pagination -components

export const Route = createFileRoute("/_authenticated/trucks/")({
  component: Trucks,
});

function Trucks() {
  const navigate = useNavigate();
  const [selectedColumns, setSelectedColumns] = useState([
    "registration",
    "make",
    "type",
    "status",
    "action",
  ]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedTruckId, setSelectedTruckId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1); // Track current page

  const handleColumnChange = (column: string) => {
    setSelectedColumns((prevSelectedColumns) =>
      prevSelectedColumns.includes(column)
        ? prevSelectedColumns.filter((col) => col !== column)
        : [...prevSelectedColumns, column],
    );
  };

  type Truck = components["schemas"]["TruckDto"];

  const {
    data: trucksResponse,
    error,
    isLoading,
  } = useGetAllRegisteredTrucks({
    page: currentPage, // Fetching based on the current page
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  const truckList: Truck[] = trucksResponse?.data ?? [];

  // Callback to show the toast after truck update
  const handleTruckUpdateSuccess = () => {
    toast.success("Truck updated successfully!");
  };

  const handleViewDetails = (truckId: string) => {
    setSelectedTruckId(truckId);
    setIsDialogOpen(true);
  };

  // Pagination Controls
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= trucksResponse?.meta?.pageCount) {
      setCurrentPage(newPage); // Update current page
    }
  };

  return (
    <div className="p-4 bg-inherit">
      {/* ToastContainer to display the toast notifications */}
      <ToastContainer />

      <div className="flex justify-between items-center mb-4 ">
        <div className="relative">
          <div className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search"
            className="pl-8 sm:w-[300px] md:w-[200px] lg:w-[300px] bg-slate-200 dark:bg-slate-800"
          />
        </div>
        <div className="flex items-center gap-2">
          <Button
            className="ml-auto bg-slate-700 text-white dark:hover:bg-slate-800 hidden md:flex"
            variant={"default"}
            onClick={() => navigate({ to: "/trucks/register-truck" })}
          >
            Register Trucks
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <SlidersHorizontal className="h-4 w-4" />
                <span className="sr-only">Show columns</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px]">
              <div className="grid gap-2 p-4">
                {["registration", "make", "type", "status", "action"].map(
                  (column) => (
                    <div className="flex items-center gap-2" key={column}>
                      <Switch
                        id={column}
                        checked={selectedColumns.includes(column)}
                        onCheckedChange={() => handleColumnChange(column)}
                      />
                      <Label htmlFor={column}>
                        {column.charAt(0).toUpperCase() +
                          column.slice(1).replace(/_/g, " ")}
                      </Label>
                    </div>
                  ),
                )}
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <Card className={"bg-slate-200 dark:bg-slate-800 rounded-lg"}>
        <Table>
          <TableHeader className={"rounded-lg"}>
            <TableRow
              className={
                "hover:bg-slate-300 dark:bg-slate-900 bg-slate-300 dark:hover:bg-slate-900 rounded-lg"
              }
            >
              {selectedColumns.includes("registration") && (
                <TableHead className="w-[200px] rounded-l-lg text-center">
                  Truck Registration
                </TableHead>
              )}
              {selectedColumns.includes("make") && (
                <TableHead className="w-[150px] text-center">Make</TableHead>
              )}
              {selectedColumns.includes("type") && (
                <TableHead className="w-[150px] text-center">Type</TableHead>
              )}
              {selectedColumns.includes("status") && (
                <TableHead className="w-[120px] text-center">Status</TableHead>
              )}
              {selectedColumns.includes("action") && (
                <TableHead className="w-[150px] rounded-r-lg text-center">
                  Action
                </TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {truckList.map((truck) => (
              <TableRow
                key={truck.id}
                className="align-middle hover:cursor-pointer"
                onClick={() => handleViewDetails(truck.id)}
              >
                {selectedColumns.includes("registration") && (
                  <TableCell className="w-[200px] text-center">
                    <span className="ml-2">{truck.registration}</span>
                  </TableCell>
                )}
                {selectedColumns.includes("make") && (
                  <TableCell className="w-[150px] text-center">
                    {truck.make}
                  </TableCell>
                )}
                {selectedColumns.includes("type") && (
                  <TableCell className="w-[150px] text-center">
                    {truck.type}
                  </TableCell>
                )}
                {selectedColumns.includes("status") && (
                  <TableCell className="w-[120px] text-center">
                    <Badge
                      variant="secondary"
                      className={`${truck.status === "ACTIVE" ? "bg-green-500" : truck.status === "INACTIVE" ? "bg-red-500" : "bg-orange-400"} text-white`}
                    >
                      {truck.status}
                    </Badge>
                  </TableCell>
                )}
                {selectedColumns.includes("action") && (
                  <TableCell className="w-[150px] text-center">
                    <Button
                      variant="outline"
                      onClick={() => handleViewDetails(truck.id)}
                    >
                      View Details
                    </Button>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>
      {/* Add Pagination Component */}

      {trucksResponse.meta?.pageCount > 1 && (
        <PagePagination
          meta={trucksResponse?.meta}
          onPageChange={handlePageChange}
        />
      )}

      {selectedTruckId && (
        <TruckDialog
          truckId={selectedTruckId}
          isOpen={isDialogOpen}
          onClose={() => setIsDialogOpen(false)}
          onSuccess={() => {
            handleTruckUpdateSuccess(); // Call the success handler
            setIsDialogOpen(false); // Close the dialog if necessary
          }}
        />
      )}
    </div>
  );
}

export default Trucks;
