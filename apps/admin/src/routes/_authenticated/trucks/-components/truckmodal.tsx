import useGetTruckDetails from "@/utils/hooks/truck/get-truck-by-id.ts";
import useUpdateTruckDetails from "@/utils/hooks/truck/use-update-truck-details.ts";
import { Dialog, DialogContent, DialogTitle } from "@radix-ui/react-dialog";
import { But<PERSON> } from "@/components/ui/button.tsx";
import { DialogFooter, DialogHeader } from "@/components/ui/dialog.tsx";
import { AlertTriangleIcon, UploadIcon, XIcon } from "lucide-react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
//import { components } from "@/api-schema";
import { Label } from "@/components/ui/label.tsx";
import { toast } from "react-toastify";
import { Image, Input, Spinner } from "@heroui/react";
import { useStorage } from "@/utils/fileStroage.ts";
import { Controller } from "react-hook-form";

//type UpdateTruckDto = components["schemas"]["UpdateTruckDto"];

interface TruckDialogProps {
  truckId: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const truckUpdateSchema = z.object({
  make: z.string().nonempty("Make is required"),
  type: z.enum(["STANDARD", "SLIDING", "TOW"]),
  status: z.enum(["ACTIVE", "INACTIVE"]),
  registration: z.string().nonempty("Registration is required"),
  capacity: z.number().min(100, "Capacity must be at least 100"),
  registrationExpiry: z.string().nonempty("Registration expiry is required"),
  model: z.string().nonempty("Model is required"),
  insuranceCompany: z.string().nonempty("Insurance company is required"),
  insuranceType: z.enum(["FULL", "PARTIAL"]),
  policyNumber: z.string().nonempty("Policy number is required"),
  frontPic: z.instanceof(FileList).optional(),
  backPic: z.instanceof(FileList).optional(),
  rightPic: z.instanceof(FileList).optional(),
  leftPic: z.instanceof(FileList).optional(),
});

export function TruckDialog({
  truckId,
  isOpen,
  onClose,
  onSuccess,
}: TruckDialogProps) {
  const { data: truckDetails, error } = useGetTruckDetails(truckId);
  const { uploadFile } = useStorage();
  const [frontPicPreview, setFrontPicPreview] = useState<string | undefined>(
    undefined,
  );
  const [backPicPreview, setBackPicPreview] = useState<string | undefined>(
    undefined,
  );
  const [rightPicPreview, setRightPicPreview] = useState<string | undefined>(
    undefined,
  );
  const [leftPicPreview, setLeftPicPreview] = useState<string | undefined>(
    undefined,
  );
  const [fullscreenImage, setFullscreenImage] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    reset,
  } = useForm({
    resolver: zodResolver(truckUpdateSchema),
    mode: "onChange",
    defaultValues: {
      make: truckDetails.make || "",
      type: truckDetails.type || "STANDARD",
      status: truckDetails.status || "ACTIVE",
      registration: truckDetails.registration || "",
      capacity: truckDetails.capacity || 0,
      registrationExpiry: truckDetails.registrationExpiry || "",
      model: truckDetails.model || "",
      insuranceCompany: truckDetails.insurance?.company || "",
      insuranceType: truckDetails.insurance?.type || "FULL",
      policyNumber: truckDetails.insurance?.policyNumber || "",
      frontPic: undefined,
      backPic: undefined,
      rightPic: undefined,
      leftPic: undefined,
    },
  });

  const { mutate, isPending } = useUpdateTruckDetails();

  const handleImageChange = (
    files: FileList | null,
    setPreview: React.Dispatch<React.SetStateAction<string | undefined>>,
  ) => {
    if (files && files.length > 0) {
      const imagePreview = URL.createObjectURL(files[0]);
      setPreview(imagePreview);
    } else {
      setPreview(undefined);
    }
  };

  useEffect(() => {
    if (truckDetails) {
      // Set previews for existing images
      if (truckDetails.files) {
        const frontPic = truckDetails.files.find(
          (f) => f.type === "VEHICLE_IMAGE_FRONT_PIC",
        );
        const backPic = truckDetails.files.find(
          (f) => f.type === "VEHICLE_IMAGE_BACK_PIC",
        );
        const rightPic = truckDetails.files.find(
          (f) => f.type === "VEHICLE_IMAGE_RIGHT_PIC",
        );
        const leftPic = truckDetails.files.find(
          (f) => f.type === "VEHICLE_IMAGE_LEFT_PIC",
        );

        if (frontPic) setFrontPicPreview(frontPic.path);
        if (backPic) setBackPicPreview(backPic.path);
        if (rightPic) setRightPicPreview(rightPic.path);
        if (leftPic) setLeftPicPreview(leftPic.path);
      }
    }
  }, [truckDetails, reset]);

  const onSubmit = async (data: z.infer<typeof truckUpdateSchema>) => {
    try {
      // Upload new images if they exist
      const frontPicUrl = data.frontPic?.[0]
        ? await uploadFile(data.frontPic[0])
        : frontPicPreview;

      const backPicUrl = data.backPic?.[0]
        ? await uploadFile(data.backPic[0])
        : backPicPreview;

      const rightPicUrl = data.rightPic?.[0]
        ? await uploadFile(data.rightPic[0])
        : rightPicPreview;

      const leftPicUrl = data.leftPic?.[0]
        ? await uploadFile(data.leftPic[0])
        : leftPicPreview;

      const payload = {
        make: data.make,
        type: data.type,
        status: data.status,
        registration: data.registration,
        capacity: data.capacity,
        registrationExpiry: new Date(data.registrationExpiry).toISOString(),
        model: data.model,
        insuranceCompany: data.insuranceCompany,
        insuranceType: data.insuranceType,
        policyNumber: data.policyNumber,
        front: frontPicUrl,
        back: backPicUrl,
        right: rightPicUrl,
        left: leftPicUrl,
      };

      mutate(
        { truckId, truck: payload },
        {
          onSuccess: () => {
            onSuccess();
            onClose();
          },
          onError: () => {
            toast.error("Failed to update truck details");
          },
        },
      );
    } catch (error) {
      toast.error("Error processing truck update");
      console.error("Error:", error);
    }
  };

  return (
    <>
      {isOpen && (
        <div className="fixed inset-0 z-20 bg-black bg-opacity-50 backdrop-blur-sm" />
      )}
      <Dialog open={isOpen}>
        <form
          onSubmit={handleSubmit((data) =>
            onSubmit(data as z.infer<typeof truckUpdateSchema>),
          )}
        >
          <DialogContent className="fixed top-1/2 left-1/2 z-50 w-full max-w-screen-lg max-h-full transform -translate-x-1/2 -translate-y-1/2 bg-slate-50 dark:bg-slate-800 p-6 rounded-lg shadow-lg transition-transform duration-300 ease-out scale-100">
            <DialogHeader>
              <DialogTitle className="text-4xl font-bold text-left text-gray-800 dark:text-white border-b-2 border-gray-300 dark:border-gray-700 py-4">
                Truck Details
              </DialogTitle>
              <button
                type="button"
                className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                onClick={onClose}
              >
                <XIcon className="w-6 h-6" />
              </button>
            </DialogHeader>
            <div className="grid grid-cols-4 gap-6 p-4 max-h-[70vh] overflow-y-auto">
              {error && (
                <div className="col-span-4 text-center text-red-600 dark:text-red-400">
                  <AlertTriangleIcon className="w-6 h-6 inline mr-2" />
                  Error fetching truck details.
                </div>
              )}
              {truckDetails && (
                <>
                  <div className="col-span-2 flex flex-col space-y-4">
                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="registration">Registration</Label>
                      <Controller
                        name="registration"
                        control={control}
                        render={({ field }) => (
                          <Input
                            id="registration"
                            placeholder="Enter registration"
                            type="text"
                            variant={"bordered"}
                            isInvalid={!!errors.registration}
                            errorMessage={errors.registration?.message}
                            color="primary"
                            {...field}
                          />
                        )}
                      />
                    </div>

                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="make">Make</Label>
                      <Controller
                        name="make"
                        control={control}
                        render={({ field }) => (
                          <Input
                            id="make"
                            placeholder="Enter truck make"
                            type="text"
                            variant={"bordered"}
                            isInvalid={!!errors.make}
                            errorMessage={errors.make?.message}
                            color="primary"
                            {...field}
                          />
                        )}
                      />
                    </div>

                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="type">Type</Label>
                      <Controller
                        name="type"
                        control={control}
                        render={({ field }) => (
                          <select
                            className="border rounded-xl border-gray-300 dark:border-gray-600 p-2 bg-slate-50 dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                            {...field}
                          >
                            <option value="STANDARD">STANDARD</option>
                            <option value="SLIDING">SLIDING</option>
                            <option value="TOW">TOW</option>
                          </select>
                        )}
                      />
                      {errors.type && (
                        <span className="text-red-500 dark:text-red-400">
                          {errors.type.message}
                        </span>
                      )}
                    </div>

                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="status">Status</Label>
                      <Controller
                        name="status"
                        control={control}
                        render={({ field }) => (
                          <select
                            className="border rounded-xl border-gray-300 dark:border-gray-600 p-2 bg-slate-50 dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                            {...field}
                            value={field.value as "ACTIVE" | "INACTIVE"}
                            onChange={(e) =>
                              field.onChange(
                                e.target.value as "ACTIVE" | "INACTIVE",
                              )
                            }
                          >
                            <option value="ACTIVE">ACTIVE</option>
                            <option value="INACTIVE">INACTIVE</option>
                          </select>
                        )}
                      />
                      {errors.status && (
                        <span className="text-red-500 dark:text-red-400">
                          {errors.status.message}
                        </span>
                      )}
                    </div>

                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="capacity">Capacity</Label>
                      <Controller
                        name="capacity"
                        control={control}
                        render={({ field }) => (
                          <Input
                            id="capacity"
                            placeholder="Enter capacity"
                            type="number"
                            variant={"bordered"}
                            isInvalid={!!errors.capacity}
                            errorMessage={errors.capacity?.message}
                            color="primary"
                            value={field.value?.toString()}
                            onChange={(e) =>
                              field.onChange(Number(e.target.value))
                            }
                          />
                        )}
                      />
                    </div>

                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="registrationExpiry">
                        Registration Expiry
                      </Label>
                      <Controller
                        name="registrationExpiry"
                        control={control}
                        render={({ field }) => (
                          <Input
                            id="registrationExpiry"
                            type="date"
                            variant={"bordered"}
                            isInvalid={!!errors.registrationExpiry}
                            errorMessage={errors.registrationExpiry?.message}
                            color="primary"
                            {...field}
                          />
                        )}
                      />
                    </div>

                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="model">Model</Label>
                      <Controller
                        name="model"
                        control={control}
                        render={({ field }) => (
                          <Input
                            id="model"
                            placeholder="Enter model"
                            type="text"
                            variant={"bordered"}
                            isInvalid={!!errors.model}
                            errorMessage={errors.model?.message}
                            color="primary"
                            {...field}
                          />
                        )}
                      />
                    </div>

                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="insuranceCompany">
                        Insurance Company
                      </Label>
                      <Controller
                        name="insuranceCompany"
                        control={control}
                        render={({ field }) => (
                          <Input
                            id="insuranceCompany"
                            placeholder="Enter insurance company"
                            type="text"
                            variant={"bordered"}
                            isInvalid={!!errors.insuranceCompany}
                            errorMessage={errors.insuranceCompany?.message}
                            color="primary"
                            {...field}
                          />
                        )}
                      />
                    </div>

                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="insuranceType">Insurance Type</Label>
                      <Controller
                        name="insuranceType"
                        control={control}
                        render={({ field }) => (
                          <select
                            className="border rounded-xl border-gray-300 dark:border-gray-600 p-2 bg-slate-50 dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                            {...field}
                          >
                            <option value="FULL">FULL</option>
                            <option value="PARTIAL">PARTIAL</option>
                          </select>
                        )}
                      />
                      {errors.insuranceType && (
                        <span className="text-red-500 dark:text-red-400">
                          {errors.insuranceType.message}
                        </span>
                      )}
                    </div>

                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="policyNumber">Policy Number</Label>
                      <Controller
                        name="policyNumber"
                        control={control}
                        render={({ field }) => (
                          <Input
                            id="policyNumber"
                            placeholder="Enter policy number"
                            type="text"
                            variant={"bordered"}
                            isInvalid={!!errors.policyNumber}
                            errorMessage={errors.policyNumber?.message}
                            color="primary"
                            {...field}
                          />
                        )}
                      />
                    </div>
                  </div>

                  <div className="col-span-2 space-y-5">
                    <div className="grid gap-2">
                      <Label htmlFor="frontPic">Front View</Label>
                      <div className="flex items-center gap-4">
                        <div className="w-36 h-20">
                          {frontPicPreview ? (
                            <Image
                              src={frontPicPreview}
                              alt="Front View"
                              className="w-36 h-20 cursor-pointer"
                              onClick={() =>
                                setFullscreenImage(frontPicPreview)
                              }
                            />
                          ) : (
                            <div className="w-36 h-20 flex flex-col items-center justify-center bg-slate-200 rounded-xl">
                              <UploadIcon className="w-8 h-8 text-slate-500" />
                              <span className="text-xs text-slate-500 mt-1">No image</span>
                            </div>
                          )}
                        </div>
                        <Controller
                          name="frontPic"
                          control={control}
                          render={({ field }) => (
                            <Input
                              id="frontPic"
                              type="file"
                              variant={"bordered"}
                              color="primary"
                              onChange={(e) => {
                                field.onChange(e.target.files);
                                handleImageChange(
                                  e.target.files,
                                  setFrontPicPreview,
                                );
                              }}
                            />
                          )}
                        />
                      </div>
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor="backPic">Back View</Label>
                      <div className="flex items-center gap-4">
                        <div className="w-36 h-20">
                          {backPicPreview ? (
                            <Image
                              src={backPicPreview}
                              alt="Back View"
                              className="w-36 h-20 cursor-pointer"
                              onClick={() => setFullscreenImage(backPicPreview)}
                            />
                          ) : (
                            <div className="w-36 h-20 flex flex-col items-center justify-center bg-slate-200 rounded-xl">
                              <UploadIcon className="w-8 h-8 text-slate-500" />
                              <span className="text-xs text-slate-500 mt-1">No image</span>
                            </div>
                          )}
                        </div>
                        <Controller
                          name="backPic"
                          control={control}
                          render={({ field }) => (
                            <Input
                              id="backPic"
                              type="file"
                              variant={"bordered"}
                              color="primary"
                              onChange={(e) => {
                                field.onChange(e.target.files);
                                handleImageChange(
                                  e.target.files,
                                  setBackPicPreview,
                                );
                              }}
                            />
                          )}
                        />
                      </div>
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor="rightPic">Right Side</Label>
                      <div className="flex items-center gap-4">
                        <div className="w-36 h-20">
                          {rightPicPreview ? (
                            <Image
                              src={rightPicPreview}
                              alt="Right Side"
                              className="w-36 h-20 cursor-pointer"
                              onClick={() =>
                                setFullscreenImage(rightPicPreview)
                              }
                            />
                          ) : (
                            <div className="w-36 h-20 flex flex-col items-center justify-center bg-slate-200 rounded-xl">
                              <UploadIcon className="w-8 h-8 text-slate-500" />
                              <span className="text-xs text-slate-500 mt-1">No image</span>
                            </div>
                          )}
                        </div>
                        <Controller
                          name="rightPic"
                          control={control}
                          render={({ field }) => (
                            <Input
                              id="rightPic"
                              type="file"
                              variant={"bordered"}
                              color="primary"
                              onChange={(e) => {
                                field.onChange(e.target.files);
                                handleImageChange(
                                  e.target.files,
                                  setRightPicPreview,
                                );
                              }}
                            />
                          )}
                        />
                      </div>
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor="leftPic">Left Side</Label>
                      <div className="flex items-center gap-4">
                        <div className="w-36 h-20">
                          {leftPicPreview ? (
                            <Image
                              src={leftPicPreview}
                              alt="Left Side"
                              className="w-36 h-20 cursor-pointer"
                              onClick={() => setFullscreenImage(leftPicPreview)}
                            />
                          ) : (
                            <div className="w-36 h-20 flex flex-col items-center justify-center bg-slate-200 rounded-xl">
                              <UploadIcon className="w-8 h-8 text-slate-500" />
                              <span className="text-xs text-slate-500 mt-1">No image</span>
                            </div>
                          )}
                        </div>
                        <Controller
                          name="leftPic"
                          control={control}
                          render={({ field }) => (
                            <Input
                              id="leftPic"
                              type="file"
                              variant={"bordered"}
                              color="primary"
                              onChange={(e) => {
                                field.onChange(e.target.files);
                                handleImageChange(
                                  e.target.files,
                                  setLeftPicPreview,
                                );
                              }}
                            />
                          )}
                        />
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>

            <DialogFooter>
              <Button
                type="submit"
                className="bg-slate-700 text-white dark:bg-slate-900"
                disabled={!isValid || isPending}
              >
                {isPending ? (
                  <Spinner className={"mr-1"} color={"white"} size={"sm"} />
                ) : null}
                Save changes
              </Button>
            </DialogFooter>
          </DialogContent>
        </form>

      </Dialog>

      {fullscreenImage && (
        <div
          className="fixed inset-0 z-[1000] flex items-center justify-center bg-black bg-opacity-70 backdrop-blur-sm pointer-events-auto"
          onClick={() => setFullscreenImage(null)}
        >
          <div className="relative z-[1500] max-w-lg max-h-xl p-4 pointer-events-auto">
            <button
              className="absolute translate-x-4 -translate-y-4 top-2 right-2 z-[2000] bg-black bg-opacity-50 text-white rounded-full p-1"
              onClick={(e) => {
                e.stopPropagation();
                setFullscreenImage(null);
              }}
            >
              <XIcon className="w-6 h-6" />
            </button>
            <Image
              src={fullscreenImage}
              alt="Fullscreen view"
              className="h-full w-full object-contain"
              onClick={(e) => {
                e.stopPropagation();
              }}
            />
          </div>
        </div>
      )}
    </>
  );
}
