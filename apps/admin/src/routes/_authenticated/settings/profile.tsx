import { createFileRoute } from "@tanstack/react-router";
import "react-toastify/dist/ReactToastify.css";
import { z } from "zod";
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Label } from "@/components/ui/label";
import { Button, Input, Spinner } from "@heroui/react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { UploadIcon } from "lucide-react";
import { useState } from "react";
import useGetMe from "@/utils/hooks/me/get-me.ts";
import useUpdateUserDetails from "@/utils/hooks/me/update-me.ts";
import { toast } from "react-toastify";
import { useStorage } from "@/utils/fileStroage";
import ConfirmationDialog from "@/components/confirmationDialog.tsx";

const profileUpdateSchema = z.object({
  firstName: z.string().nonempty("First name is required"),
  lastName: z.string().nonempty("Last name is required"),
  email: z.string().email("Invalid email address"),
  profilePic: z.instanceof(FileList).optional(), // File input
});

type ProfileFormValues = z.infer<typeof profileUpdateSchema>;

export const Route = createFileRoute("/_authenticated/settings/profile")({
  component: Profile,
});

function Profile() {
  const { data: getData } = useGetMe();
  const { mutate, isPending } = useUpdateUserDetails();
  const { uploadFile } = useStorage(); // Use the file storage hook
  const [isEditing, setIsEditing] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  const handleEditing = () => {
    setIsEditing(true);
  };

  const [profilePreview, setProfilePreview] = useState<string | null>(
    getData?.profilePic || null,
  );

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    control,
  } = useForm<ProfileFormValues>({
    resolver: zodResolver(profileUpdateSchema),
    mode: "onChange",
    defaultValues: {
      firstName: getData?.firstName || "",
      lastName: getData?.lastName || "",
      email: getData?.email || "",
      profilePic: undefined,
    },
  });

  const handleImageChange = (
    files: FileList | null,
    setPreview: React.Dispatch<React.SetStateAction<string | null>>,
  ) => {
    if (files && files.length > 0) {
      const imagePreview = URL.createObjectURL(files[0]);
      setPreview(imagePreview); // Show preview
    } else {
      setPreview(null);
    }
  };

  const handleSaveClick = () => {
    setShowConfirmDialog(true);
  };

  const onSubmit = async (data: ProfileFormValues) => {
    try {
      let profilePicUrl = getData?.profilePic || "";

      // Upload profile picture if changed
      if (data.profilePic && data.profilePic[0]) {
        profilePicUrl = await uploadFile(data.profilePic[0]); // Get the uploaded file URL
      }

      // Prepare payload
      const updatedUser = {
        firstName: data.firstName,
        lastName: data.lastName,
        profilePic: profilePicUrl, // Use uploaded file URL
      };

      // Update user details
      mutate(
        { userId: getData?.id, user: updatedUser },
        {
          onSuccess: () => {
            toast.success("Profile updated successfully!");
          },
          onError: () => {
            toast.error("Error updating profile.");
          },
        },
      );
    } catch (error) {
      console.error("Error uploading file:", error);
      toast.error("Failed to upload profile picture.");
    }
  };

  return (
    <>
      <div className="flex flex-col min-h-screen bg-inherit px-6 py-4">
        <form onSubmit={handleSubmit(handleSaveClick)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="flex flex-col gap-4">
              <div className="grid gap-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  isDisabled={!isEditing}
                  id="firstName"
                  placeholder="Enter first name"
                  type="text"
                  variant="bordered"
                  isRequired
                  isInvalid={!!errors.firstName}
                  errorMessage={errors.firstName?.message}
                  color="primary"
                  {...register("firstName")}
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  isDisabled={!isEditing}
                  id="lastName"
                  placeholder="Enter last name"
                  type="text"
                  variant="bordered"
                  isRequired
                  isInvalid={!!errors.lastName}
                  errorMessage={errors.lastName?.message}
                  color="primary"
                  {...register("lastName")}
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  isDisabled={true}
                  id="email"
                  placeholder="Enter email"
                  type="email"
                  variant="bordered"
                  isRequired
                  isInvalid={!!errors.email}
                  errorMessage={errors.email?.message}
                  color="primary"
                  {...register("email")}
                />
              </div>
            </div>

            {/* Right Column */}
            <div className="flex flex-col gap-4">
              <div className="grid gap-2">
                <Label htmlFor="profile-picture">Profile Picture</Label>
                <div className="flex items-center gap-4">
                  <Avatar className="w-20 h-20">
                    <AvatarImage
                      src={profilePreview || "/placeholder-user.jpg"}
                      alt="Profile Picture"
                    />
                    <AvatarFallback>
                      <UploadIcon className="w-8 h-8 text-muted-foreground" />
                    </AvatarFallback>
                  </Avatar>
                  <Controller
                    name="profilePic"
                    control={control}
                    render={({ field }) => (
                      <Input
                        isDisabled={!isEditing}
                        id="profile-picture"
                        type="file"
                        accept="image/*"
                        variant="bordered"
                        onChange={(e) => {
                          handleImageChange(e.target.files, setProfilePreview);
                          field.onChange(e.target.files);
                        }}
                        ref={field.ref}
                        isInvalid={!!errors.profilePic}
                        errorMessage={errors.profilePic?.message}
                      />
                    )}
                  />
                </div>
                {errors.profilePic && (
                  <p className="text-red-400 text-xs">
                    {errors.profilePic.message}
                  </p>
                )}
              </div>
            </div>
          </div>

          <div className="w-full md:w-fit px-4 md:ml-auto">
            <Button
              type="button"
              radius={"lg"}
              className={`${isEditing ? "hidden" : ""} bg-slate-700 text-white   dark:bg-slate-900`}
              onPress={handleEditing}
            >
              Enable Editing Mode
            </Button>

            <Button
              type="submit"
              className={`${isEditing ? "" : "hidden"} bg-slate-700 text-white dark:bg-slate-900`}
            >
              {isPending || isSubmitting ? (
                <Spinner className={"mr-1"} color={"white"} size={"sm"} />
              ) : (
                ""
              )}
              Save changes
            </Button>
          </div>
        </form>
      </div>

      <ConfirmationDialog
        isOpen={showConfirmDialog}
        message="Are you sure you want to save changes?"
        onConfirm={() => {
          setShowConfirmDialog(false);
          handleSubmit(onSubmit)();
        }}
        onCancel={() => setShowConfirmDialog(false)}
      />
    </>
  );
}

export default Profile;
