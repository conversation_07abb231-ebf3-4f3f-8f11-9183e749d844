import { createFileRoute } from "@tanstack/react-router";
import { <PERSON><PERSON>, Input, Spinner } from "@heroui/react";
import { Label } from "@/components/ui/label";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import useUpdateCharges from "@/utils/hooks/chargers/use-update-charges.ts";
import useGetDestinationCharges from "@/utils/hooks/chargers/get-charges.ts";
import { components } from "@/api-schema";
import { useState } from "react";
import ConfirmationDialog from "@/components/confirmationDialog.tsx";

// Define the schema for validation using Zod
const chargesUpdateSchema = z.object({
  baseFare: z.number().nonnegative("Base charges must be positive"),
  farePerKm: z.number().nonnegative("Per kilometer charges must be positive"),
  extraFare: z.number().nonnegative("Extra charges must be positive"),
  cancellation: z.number().nonnegative("Cancellation charges must be positive"),
  farePerMin: z.number().nonnegative("Fare per minute must be positive"),
  platformFee: z.number().nonnegative("Platform fee must be positive"),
  tax: z.number().nonnegative("Tax must be positive"),
  currency: z.string().min(1),
  id: z.string().min(1),
});

// Define the type based on the schema
type ChargesDto = components["schemas"]["TripSettingsDto"];

export const Route = createFileRoute(
  "/_authenticated/settings/illegal-parking-charges",
)({
  component: IllegalParkingCharges,
});

function IllegalParkingCharges() {
  const { data: getData } = useGetDestinationCharges("ILLEGAL_PARKING");

  console.log("Illegal charges data:", getData.baseFare); // Debugging line

  const { register, handleSubmit, formState } = useForm<ChargesDto>({
    resolver: zodResolver(chargesUpdateSchema),
    mode: "onChange", // This will trigger validation on every change
    defaultValues: {
      baseFare: getData.baseFare || 0,
      farePerKm: getData.farePerKm || 0,
      extraFare: getData.extraFare || 0,
      cancellation: getData.cancellation || 0,
      farePerMin: getData.farePerMin || 0,
      platformFee: getData.platformFee || 0,
      tax: getData.tax || 0,
      currency: getData.currency || "",
      id: getData.id || "",
    },
  });

  const { mutate, isPending } = useUpdateCharges("ILLEGAL_PARKING");

  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingData, setPendingData] = useState<ChargesDto | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  const handleEditing = () => {
    setIsEditing(true);
  };

  const handleSaveClick = (data: ChargesDto) => {
    setPendingData(data);
    setShowConfirmDialog(true);
  };

  const onSubmit = () => {
    if (!pendingData) return;

    setShowConfirmDialog(false);
    mutate(pendingData, {
      onSuccess: () => {
        toast.success("Charges details updated successfully");
        setIsEditing(false);
      },
      onError: () => {
        toast.error("Error updating charges details");
      },
    });
  };

  return (
    <>
      <div className="flex flex-col min-h-screen bg-inherit px-6 py-4">
        <form onSubmit={handleSubmit(handleSaveClick)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="flex flex-col gap-4">
              {/* Base Charges Field */}
              <div className="grid gap-2">
                <Label htmlFor="baseCharges">Base Charges</Label>
                <Input
                  isDisabled={!isEditing}
                  id="baseCharges"
                  placeholder="Enter base charges"
                  type="number"
                  variant="bordered"
                  isRequired
                  isInvalid={!!formState.errors.baseFare}
                  errorMessage={formState.errors.baseFare?.message}
                  color="primary"
                  {...register("baseFare", {
                    required: "Base charges are required",
                    valueAsNumber: true,
                  })}
                />
              </div>

              {/* Per Kilometer Charges Field */}
              <div className="grid gap-2">
                <Label htmlFor="perKilometerCharges">
                  Per Kilometer Charges
                </Label>
                <Input
                  isDisabled={!isEditing}
                  id="perKilometerCharges"
                  placeholder="Enter per kilometer charges"
                  type="number"
                  variant="bordered"
                  isRequired
                  isInvalid={!!formState.errors.farePerKm}
                  errorMessage={formState.errors.farePerKm?.message}
                  color="primary"
                  {...register("farePerKm", {
                    required: "Per kilometer charges are required",
                    valueAsNumber: true,
                  })}
                />
              </div>

              {/* Extra Charges Field */}
              <div className="grid gap-2">
                <Label htmlFor="extraCharges">Extra Charges</Label>
                <Input
                  isDisabled={!isEditing}
                  id="extraCharges"
                  placeholder="Enter extra charges"
                  type="number"
                  variant="bordered"
                  isRequired
                  isInvalid={!!formState.errors.extraFare}
                  errorMessage={formState.errors.extraFare?.message}
                  color="primary"
                  {...register("extraFare", {
                    required: "Extra charges are required",
                    valueAsNumber: true,
                  })}
                />
              </div>

              {/* Cancellation Charges Field */}
              <div className="grid gap-2">
                <Label htmlFor="cancellationCharges">
                  Cancellation Charges
                </Label>
                <Input
                  isDisabled={!isEditing}
                  id="cancellationCharges"
                  placeholder="Enter cancellation charges"
                  type="number"
                  variant="bordered"
                  isRequired
                  isInvalid={!!formState.errors.cancellation}
                  errorMessage={formState.errors.cancellation?.message}
                  color="primary"
                  {...register("cancellation", {
                    required: "Cancellation charges are required",
                    valueAsNumber: true,
                  })}
                />
              </div>
            </div>

            {/* Right Column */}
            <div className="flex flex-col gap-4">
              {/* Fare Per Minute Field */}
              <div className="grid gap-2">
                <Label htmlFor="farePerMin">Fare Per Minute</Label>
                <Input
                  isDisabled={!isEditing}
                  id="farePerMin"
                  placeholder="Enter fare per minute"
                  type="number"
                  variant="bordered"
                  isRequired
                  isInvalid={!!formState.errors.farePerMin}
                  errorMessage={formState.errors.farePerMin?.message}
                  color="primary"
                  {...register("farePerMin", {
                    required: "Fare per minute is required",
                    valueAsNumber: true,
                  })}
                />
              </div>

              {/* Platform Fee Field */}
              <div className="grid gap-2">
                <Label htmlFor="platformFee">Platform Fee</Label>
                <Input
                  isDisabled={!isEditing}
                  id="platformFee"
                  placeholder="Enter platform fee"
                  type="number"
                  variant="bordered"
                  isRequired
                  isInvalid={!!formState.errors.platformFee}
                  errorMessage={formState.errors.platformFee?.message}
                  color="primary"
                  {...register("platformFee", {
                    required: "Platform fee is required",
                    valueAsNumber: true,
                  })}
                />
              </div>

              {/* Tax Field */}
              <div className="grid gap-2">
                <Label htmlFor="tax">Tax</Label>
                <Input
                  isDisabled={!isEditing}
                  id="tax"
                  placeholder="Enter tax"
                  type="number"
                  variant="bordered"
                  isRequired
                  isInvalid={!!formState.errors.tax}
                  errorMessage={formState.errors.tax?.message}
                  color="primary"
                  {...register("tax", {
                    required: "Tax is required",
                    valueAsNumber: true,
                  })}
                />
              </div>

              {/* Currency Field */}
              <div className="grid gap-2">
                <Label htmlFor="currency">Currency</Label>
                <Input
                  id="currency"
                  placeholder="Enter currency"
                  variant="bordered"
                  isRequired
                  isDisabled={true}
                  isInvalid={!!formState.errors.currency}
                  errorMessage={formState.errors.currency?.message}
                  color="primary"
                  {...register("currency", {
                    required: "Currency is required",
                  })}
                />
              </div>

              {/* ID Field */}
              <div className=" gap-2 hidden">
                <Label htmlFor="id">ID</Label>
                <Input
                  isDisabled={!isEditing}
                  id="id"
                  placeholder="Enter ID"
                  variant="bordered"
                  isRequired
                  isInvalid={!!formState.errors.id}
                  errorMessage={formState.errors.id?.message}
                  color="primary"
                  {...register("id", {
                    required: "ID is required",
                  })}
                />
              </div>
            </div>
          </div>

          <div className="w-full md:w-fit px-4 md:ml-auto">
            <Button
              type="button"
              radius={"lg"}
              className={`${isEditing ? "hidden" : ""} bg-slate-700 text-white   dark:bg-slate-900`}
              onPress={handleEditing}
            >
              Enable Editing Mode
            </Button>

            <Button
              type="submit"
              className={`${isEditing ? "" : "hidden"} bg-slate-700 text-white dark:bg-slate-900`}
            >
              {isPending ? (
                <Spinner className={"mr-1"} color={"white"} size={"sm"} />
              ) : (
                ""
              )}
              Save changes
            </Button>
          </div>
        </form>
      </div>

      <ConfirmationDialog
        isOpen={showConfirmDialog}
        message="Are you sure you want to save changes?"
        onConfirm={onSubmit}
        onCancel={() => setShowConfirmDialog(false)}
      />
    </>
  );
}
