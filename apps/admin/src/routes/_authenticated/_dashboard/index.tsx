import { createFileRoute } from "@tanstack/react-router";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CartesianGrid, Line, LineChart, XAxis } from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { ClassAttributes, HTMLAttributes } from "react";
import { JSX } from "react/jsx-runtime";
import useDashboard from "@/utils/hooks/totalCounts/dashboard-counts.ts";
import { CircleDollarSign, CompassIcon, ShipWheel, Users } from "lucide-react";

export const Route = createFileRoute("/_authenticated/_dashboard/")({
  component: Dashboard,
});

function Dashboard() {
  const { data: counts } = useDashboard();
  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-1 grid gap-6 p-4 md:gap-8 md:p-6">
        <div className="grid grid-cols-2 gap-6 md:grid-cols-4 animate-fade-down">
          <Card
            className={
              "bg-slate-300 dark:bg-slate-700 transform transition-transform hover:scale-110 border-0 shadow-md hover:shadow-lg"
            }
          >
            <CardHeader>
              <div className={"flex mb-1"}>
                <CircleDollarSign className={"mr-1 text-muted-foreground"} />
                <CardDescription className={"font-bold"}>
                  Revenue
                </CardDescription>
              </div>

              <CardTitle>${counts.revenue}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xs text-muted-foreground">
                +15% from last month
              </div>
            </CardContent>
          </Card>
          <Card
            className={
              "bg-slate-300 dark:bg-slate-700 transform transition-transform hover:scale-110 border-0 shadow-md hover:shadow-lg"
            }
          >
            <CardHeader>
              <div className={"flex mb-1"}>
                <ShipWheel className={"mr-1 text-muted-foreground"} />
                <CardDescription className={"font-bold"}>
                  Drivers
                </CardDescription>
              </div>
              <CardTitle>{counts.totalDrivers}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xs text-muted-foreground">
                +5% from last month
              </div>
            </CardContent>
          </Card>
          <Card
            className={
              "bg-slate-300 dark:bg-slate-700 transform transition-transform hover:scale-110 border-0 shadow-md hover:shadow-lg"
            }
          >
            <CardHeader>
              <div className={"flex mb-1"}>
                <Users className={"mr-1 text-muted-foreground"} />
                <CardDescription className={"font-bold"}>
                  Clients
                </CardDescription>
              </div>
              <CardTitle>{counts.totalClients}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xs text-muted-foreground">
                +10% from last month
              </div>
            </CardContent>
          </Card>
          <Card
            className={
              "bg-slate-300 dark:bg-slate-700 transform transition-transform hover:scale-110 border-0 shadow-md hover:shadow-lg"
            }
          >
            <CardHeader>
              <div className={"flex mb-1"}>
                <CompassIcon className={"mr-1 text-muted-foreground"} />
                <CardDescription className={"font-bold"}>Trips</CardDescription>
              </div>

              <CardTitle>{counts.completedTrips}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-xs text-muted-foreground">
                +20% from last month
              </div>
            </CardContent>
          </Card>
        </div>
        <div className="grid gap-6 md:grid-cols-2">
          <Card
            className={
              "bg-slate-300 dark:bg-slate-700 border-0 animate-fade-right shadow-xl"
            }
          >
            <CardHeader>
              <CardTitle>Revenue</CardTitle>
            </CardHeader>
            <CardContent>
              <LinechartChart className="aspect-[4/3]" />
            </CardContent>
          </Card>
          <div className="grid gap-6">
            <Card
              className={
                "bg-slate-300 dark:bg-slate-700 border-0 animate-fade-left  shadow-xl"
              }
            >
              <CardHeader>
                <CardTitle>Recent Trips</CardTitle>
              </CardHeader>
              <CardContent className="grid gap-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">John Doe</div>
                    <div className="text-sm text-muted-foreground">
                      <EMAIL>
                    </div>
                  </div>
                  <div className="font-medium">$150</div>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Jane Smith</div>
                    <div className="text-sm text-muted-foreground">
                      <EMAIL>
                    </div>
                  </div>
                  <div className="font-medium">$200</div>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Bob Johnson</div>
                    <div className="text-sm text-muted-foreground">
                      <EMAIL>
                    </div>
                  </div>
                  <div className="font-medium">$180</div>
                </div>
              </CardContent>
            </Card>
            <Card
              className={
                "bg-slate-300 dark:bg-slate-700 border-0 animate-fade-left shadow-xl"
              }
            >
              <CardHeader>
                <CardTitle>Jane Cooper's Trips</CardTitle>
              </CardHeader>
              <CardContent className="grid gap-4">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">San Francisco</div>
                    <div className="text-sm text-muted-foreground">
                      May 1, 2023
                    </div>
                  </div>
                  <div className="font-medium">$75</div>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Los Angeles</div>
                    <div className="text-sm text-muted-foreground">
                      June 15, 2023
                    </div>
                  </div>
                  <div className="font-medium">$120</div>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">Seattle</div>
                    <div className="text-sm text-muted-foreground">
                      July 30, 2023
                    </div>
                  </div>
                  <div className="font-medium">$90</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}

function LinechartChart(
  props: JSX.IntrinsicAttributes &
    ClassAttributes<HTMLDivElement> &
    HTMLAttributes<HTMLDivElement>,
) {
  return (
    <div {...props}>
      <ChartContainer
        config={{
          desktop: {
            label: "Desktop",
            color: "hsl(var(--chart-1))",
          },
        }}
      >
        <LineChart
          accessibilityLayer
          data={[
            { month: "January", desktop: 186 },
            { month: "February", desktop: 305 },
            { month: "March", desktop: 237 },
            { month: "April", desktop: 73 },
            { month: "May", desktop: 209 },
            { month: "June", desktop: 214 },
          ]}
          margin={{
            left: 12,
            right: 12,
          }}
        >
          <CartesianGrid vertical={false} />
          <XAxis
            dataKey="month"
            tickLine={false}
            axisLine={false}
            tickMargin={8}
            tickFormatter={(value) => value.slice(0, 3)}
          />
          <ChartTooltip
            cursor={false}
            content={<ChartTooltipContent hideLabel />}
          />
          <Line
            dataKey="desktop"
            type="natural"
            stroke="var(--color-desktop)"
            strokeWidth={2}
            dot={false}
          />
        </LineChart>
      </ChartContainer>
    </div>
  );
}
