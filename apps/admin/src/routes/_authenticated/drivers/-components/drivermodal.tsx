import { useEffect, useState } from "react";
import useGetDriverDetails from "@/utils/hooks/drivers/get-driver-by-id.ts";
import useUpdateDriverDetails from "@/utils/hooks/drivers/use-update-driver-details.ts";
import { <PERSON><PERSON>, DialogContent, DialogTitle } from "@radix-ui/react-dialog";
import { Button } from "@/components/ui/button.tsx";
import { <PERSON>alogFooter, DialogHeader } from "@/components/ui/dialog.tsx";
import { AlertTriangleIcon, UploadIcon, XIcon } from "lucide-react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
//import { components } from "@/api-schema";
import { Input, Spinner, Image } from "@heroui/react";
import { Label } from "@/components/ui/label.tsx";
import { toast } from "react-toastify";
import { Controller } from "react-hook-form";
import { useStorage } from "@/utils/fileStroage.ts";

/*type DriverDto = components["schemas"]["UpdateDriverDto"] & {
  user: {
    firstName: string;
    lastName: string;
  };
};*/

interface DriverDialogProps {
  driverId: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  readStatus?: boolean;
}

const driverUpdateSchema = z.object({
  firstName: z.string().nonempty("First name is required"),
  lastName: z.string().nonempty("Last name is required"),
  mobile: z.string().nonempty("Mobile number is required"),
  address: z.string().nonempty("Address is required"),
  drivingLicence: z.string().nonempty("Driving licence is required"),
  status: z.enum(["ACTIVE", "INACTIVE"]),
  dob: z.string().nonempty("Date of birth is required"),
  transportLicence: z.string().nonempty("Transport licence is required"),
  drivingLicencePic: z.instanceof(FileList).optional(),
  idCardFrontPic: z.instanceof(FileList).optional(),
  idCardBackPic: z.instanceof(FileList).optional(),
  transportLicencePic: z.instanceof(FileList).optional(),
});

export function DriverDialog({
  driverId,
  isOpen,
  onClose,
  onSuccess,
  readStatus,
}: DriverDialogProps) {
  const { data: driverDetails, error } = useGetDriverDetails(driverId);
  const { uploadFile } = useStorage();
  const [drivingLicencePicPreview, setDrivingLicencePicPreview] = useState<
    string | undefined
  >(undefined);
  const [idCardFrontPicPreview, setIdCardFrontPicPreview] = useState<
    string | undefined
  >(undefined);
  const [idCardBackPicPreview, setIdCardBackPicPreview] = useState<
    string | undefined
  >(undefined);
  const [transportLicencePicPreview, setTransportLicencePicPreview] = useState<
    string | undefined
  >(undefined);
  const [fullscreenImage, setFullscreenImage] = useState<string | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors, isValid },
    reset,
  } = useForm({
    resolver: zodResolver(driverUpdateSchema),
    mode: "onChange",
    defaultValues: {
      firstName: driverDetails.user?.firstName || "",
      lastName: driverDetails.user?.lastName || "",
      mobile: driverDetails.user?.mobile || "",
      address: driverDetails?.address || "",
      drivingLicence: driverDetails?.drivingLicence || "",
      status: driverDetails?.status || "ACTIVE",
      dob: driverDetails?.dob?.split("T")[0] || "",
      transportLicence: driverDetails?.transportLicence || "",
      drivingLicencePic: undefined,
      idCardFrontPic: undefined,
      idCardBackPic: undefined,
      transportLicencePic: undefined,
    },
  });

  const handleImageChange = (
    files: FileList | null,
    setPreview: React.Dispatch<React.SetStateAction<string | undefined>>,
  ) => {
    if (files && files.length > 0) {
      const imagePreview = URL.createObjectURL(files[0]);
      setPreview(imagePreview);
    } else {
      setPreview(undefined);
    }
  };

  useEffect(() => {
    if (driverDetails) {
      // Set previews for existing images
      if (driverDetails.files) {
        const drivingLicencePic = driverDetails.files.find(
          (f) => f.type === "DRIVING_LICENCE",
        );
        const idCardFrontPic = driverDetails.files.find(
          (f) => f.type === "ID_CARD_FRONT_PIC",
        );
        const idCardBackPic = driverDetails.files.find(
          (f) => f.type === "ID_CARD_BACK_PIC",
        );
        const transportLicencePic = driverDetails.files.find(
          (f) => f.type === "TRANSPORT_LICENCE",
        );

        if (drivingLicencePic)
          setDrivingLicencePicPreview(drivingLicencePic.path);
        if (idCardFrontPic) setIdCardFrontPicPreview(idCardFrontPic.path);
        if (idCardBackPic) setIdCardBackPicPreview(idCardBackPic.path);
        if (transportLicencePic)
          setTransportLicencePicPreview(transportLicencePic.path);
      }
    }
  }, [driverDetails, reset]);

  const { mutate, isPending } = useUpdateDriverDetails();

  const onSubmit = async (data: z.infer<typeof driverUpdateSchema>) => {
    try {
      // Upload new images if they exist

      const drivingLicencePicUrl = data.drivingLicencePic?.[0]
        ? await uploadFile(data.drivingLicencePic[0])
        : drivingLicencePicPreview;

      const idCardFrontPicUrl = data.idCardFrontPic?.[0]
        ? await uploadFile(data.idCardFrontPic[0])
        : idCardFrontPicPreview;

      const idCardBackPicUrl = data.idCardBackPic?.[0]
        ? await uploadFile(data.idCardBackPic[0])
        : idCardBackPicPreview;

      const transportLicencePicUrl = data.transportLicencePic?.[0]
        ? await uploadFile(data.transportLicencePic[0])
        : transportLicencePicPreview;

      const payload = {
        firstName: data.firstName,
        lastName: data.lastName,
        mobile: data.mobile,
        address: data.address,
        drivingLicence: data.drivingLicence,
        status: data.status,
        dob: new Date(data.dob).toISOString(),
        transportLicence: data.transportLicence,
        drivingLicencePic: drivingLicencePicUrl,
        idCardFrontPic: idCardFrontPicUrl,
        idCardBackPic: idCardBackPicUrl,
        transportLicencePic: transportLicencePicUrl,
      };

      mutate(
        { driverId, driver: payload },
        {
          onSuccess: () => {
            onSuccess();
            onClose();
          },
          onError: () => {
            toast.error("Failed to update driver details");
          },
        },
      );
    } catch (error) {
      toast.error("Error processing driver update");
      console.error("Error:", error);
    }
  };

  return (
    <>
      {isOpen && (
        <div className="fixed inset-0 z-20 bg-black bg-opacity-50 backdrop-blur-sm" />
      )}
      <Dialog open={isOpen}>
        <form
          onSubmit={handleSubmit((data) =>
            onSubmit(data as z.infer<typeof driverUpdateSchema>),
          )}
        >
          <DialogContent className="fixed top-1/2 left-1/2 z-50 w-full max-w-screen-lg max-h-full transform -translate-x-1/2 -translate-y-1/2 bg-slate-50 dark:bg-slate-800 p-6 rounded-lg shadow-lg transition-transform duration-300 ease-out scale-100">
            <DialogHeader>
              <DialogTitle className="text-4xl font-bold text-left text-gray-800 dark:text-white border-b-2 border-gray-300 dark:border-gray-700 py-4">
                Driver Details
              </DialogTitle>
              <button
                type="button"
                className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                onClick={onClose}
              >
                <XIcon className="w-6 h-6" />
              </button>
            </DialogHeader>
            <div className="grid grid-cols-4 gap-6 p-4 max-h-[70vh] overflow-y-auto">
              {error && (
                <div className="col-span-4 text-center text-red-600 dark:text-red-400">
                  <AlertTriangleIcon className="w-6 h-6 inline mr-2" />
                  Error fetching driver details.
                </div>
              )}
              {driverDetails && (
                <>
                  <div className="col-span-2 flex flex-col space-y-4">
                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="firstName">First Name</Label>
                      <Controller
                        name="firstName"
                        control={control}
                        render={({ field }) => (
                          <Input
                            id="firstName"
                            placeholder="Enter first name"
                            type="text"
                            variant={"bordered"}
                            isInvalid={!!errors.firstName}
                            errorMessage={errors.firstName?.message}
                            color="primary"
                            isDisabled={readStatus}
                            {...field}
                          />
                        )}
                      />
                    </div>

                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="lastName">Last Name</Label>
                      <Controller
                        name="lastName"
                        control={control}
                        render={({ field }) => (
                          <Input
                            id="lastName"
                            placeholder="Enter last name"
                            type="text"
                            variant={"bordered"}
                            isInvalid={!!errors.lastName}
                            errorMessage={errors.lastName?.message}
                            color="primary"
                            isDisabled={readStatus}
                            {...field}
                          />
                        )}
                      />
                    </div>

                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        placeholder="Email"
                        type="email"
                        variant={"bordered"}
                        color="primary"
                        isDisabled={true}
                        defaultValue={driverDetails.user?.email}
                      />
                    </div>

                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="mobile">Mobile</Label>
                      <Controller
                        name="mobile"
                        control={control}
                        render={({ field }) => (
                          <Input
                            id="mobile"
                            placeholder="Enter mobile number"
                            type="text"
                            variant={"bordered"}
                            isInvalid={!!errors.mobile}
                            errorMessage={errors.mobile?.message}
                            color="primary"
                            isDisabled={readStatus}
                            {...field}
                          />
                        )}
                      />
                    </div>

                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="address">Address</Label>
                      <Controller
                        name="address"
                        control={control}
                        render={({ field }) => (
                          <Input
                            id="address"
                            placeholder="Enter address"
                            type="text"
                            variant={"bordered"}
                            isInvalid={!!errors.address}
                            errorMessage={errors.address?.message}
                            color="primary"
                            isDisabled={readStatus}
                            {...field}
                          />
                        )}
                      />
                    </div>

                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="drivingLicence">Driving Licence</Label>
                      <Controller
                        name="drivingLicence"
                        control={control}
                        render={({ field }) => (
                          <Input
                            id="drivingLicence"
                            placeholder="Enter driving licence"
                            type="text"
                            variant={"bordered"}
                            isInvalid={!!errors.drivingLicence}
                            errorMessage={errors.drivingLicence?.message}
                            color="primary"
                            isDisabled={readStatus}
                            {...field}
                          />
                        )}
                      />
                    </div>

                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="transportLicence">
                        Transport Licence
                      </Label>
                      <Controller
                        name="transportLicence"
                        control={control}
                        render={({ field }) => (
                          <Input
                            id="transportLicence"
                            placeholder="Enter transport licence"
                            type="text"
                            variant={"bordered"}
                            isInvalid={!!errors.transportLicence}
                            errorMessage={errors.transportLicence?.message}
                            color="primary"
                            isDisabled={readStatus}
                            {...field}
                          />
                        )}
                      />
                    </div>

                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="status">Status</Label>
                      <Controller
                        name="status"
                        control={control}
                        render={({ field }) => (
                          <select
                            className="border rounded-xl border-gray-300 dark:border-gray-600 p-2 bg-slate-50 dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                            {...field}
                            value={field.value as "ACTIVE" | "INACTIVE"}
                            onChange={(e) =>
                              field.onChange(
                                e.target.value as "ACTIVE" | "INACTIVE",
                              )
                            }
                            disabled={readStatus}
                          >
                            <option value="ACTIVE">ACTIVE</option>
                            <option value="INACTIVE">INACTIVE</option>
                          </select>
                        )}
                      />
                      {errors.status && (
                        <span className="text-red-500 dark:text-red-400">
                          {errors.status.message}
                        </span>
                      )}
                    </div>

                    <div className="grid w-full max-w-sm items-center gap-1.5">
                      <Label htmlFor="dob">Date of Birth</Label>
                      <Controller
                        name="dob"
                        control={control}
                        render={({ field }) => (
                          <Input
                            id="dob"
                            type="date"
                            variant={"bordered"}
                            isInvalid={!!errors.dob}
                            errorMessage={errors.dob?.message}
                            color="primary"
                            isDisabled={readStatus}
                            {...field}
                          />
                        )}
                      />
                    </div>
                  </div>

                  <div className="col-span-2 space-y-5">
                    <div className="grid gap-2">
                      <Label htmlFor="drivingLicencePic">
                        Driving Licence Picture
                      </Label>
                      <div className="flex items-center gap-4">
                        <div className="w-36 h-20">
                          {drivingLicencePicPreview ? (
                            <Image
                              src={drivingLicencePicPreview}
                              alt="Driving Licence Picture"
                              className="w-36 h-20 cursor-pointer"
                              onClick={() =>
                                setFullscreenImage(drivingLicencePicPreview)
                              }
                            />
                          ) : (
                            <div className="w-28 h-20 flex items-center justify-center bg-slate-300 rounded-xl">
                              <UploadIcon className="w-10 h-10" />
                            </div>
                          )}
                        </div>
                        <Controller
                          name="drivingLicencePic"
                          control={control}
                          render={({ field }) => (
                            <Input
                              id="drivingLicencePic"
                              type="file"
                              variant={"bordered"}
                              color="primary"
                              isDisabled={readStatus}
                              onChange={(e) => {
                                field.onChange(e.target.files);
                                handleImageChange(
                                  e.target.files,
                                  setDrivingLicencePicPreview,
                                );
                              }}
                            />
                          )}
                        />
                      </div>
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor="idCardFrontPic">
                        ID Card Front Picture
                      </Label>
                      <div className="flex items-center gap-4">
                        <div className="w-36 h-20">
                          {idCardFrontPicPreview ? (
                            <Image
                              src={idCardFrontPicPreview}
                              alt="ID Card Front Picture"
                              className="w-36 h-20 cursor-pointer"
                              onClick={() =>
                                setFullscreenImage(idCardFrontPicPreview)
                              }
                            />
                          ) : (
                            <div className="w-28 h-20 flex items-center justify-center bg-slate-300 rounded-xl">
                              <UploadIcon className="w-10 h-10" />
                            </div>
                          )}
                        </div>
                        <Controller
                          name="idCardFrontPic"
                          control={control}
                          render={({ field }) => (
                            <Input
                              id="idCardFrontPic"
                              type="file"
                              variant={"bordered"}
                              color="primary"
                              isDisabled={readStatus}
                              onChange={(e) => {
                                field.onChange(e.target.files);
                                handleImageChange(
                                  e.target.files,
                                  setIdCardFrontPicPreview,
                                );
                              }}
                            />
                          )}
                        />
                      </div>
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor="idCardBackPic">
                        ID Card Back Picture
                      </Label>
                      <div className="flex items-center gap-4">
                        <div className="w-36 h-20">
                          {idCardBackPicPreview ? (
                            <Image
                              src={idCardBackPicPreview}
                              alt="ID Card Back Picture"
                              className="w-36 h-20 cursor-pointer"
                              onClick={() =>
                                setFullscreenImage(idCardBackPicPreview)
                              }
                            />
                          ) : (
                            <div className="w-28 h-20 flex items-center justify-center bg-slate-300 rounded-xl">
                              <UploadIcon className="w-10 h-10" />
                            </div>
                          )}
                        </div>
                        <Controller
                          name="idCardBackPic"
                          control={control}
                          render={({ field }) => (
                            <Input
                              id="idCardBackPic"
                              type="file"
                              variant={"bordered"}
                              color="primary"
                              isDisabled={readStatus}
                              onChange={(e) => {
                                field.onChange(e.target.files);
                                handleImageChange(
                                  e.target.files,
                                  setIdCardBackPicPreview,
                                );
                              }}
                            />
                          )}
                        />
                      </div>
                    </div>

                    <div className="grid gap-2">
                      <Label htmlFor="transportLicencePic">
                        Transport Licence Picture
                      </Label>
                      <div className="flex items-center gap-4">
                        <div className="w-36 h-20">
                          {transportLicencePicPreview ? (
                            <Image
                              src={transportLicencePicPreview}
                              alt="Transport Licence Picture"
                              className="w-36 h-20 cursor-pointer"
                              onClick={() =>
                                setFullscreenImage(transportLicencePicPreview)
                              }
                            />
                          ) : (
                            <div className="w-28 h-20 flex items-center justify-center bg-slate-300 rounded-xl">
                              <UploadIcon className="w-10 h-10" />
                            </div>
                          )}
                        </div>
                        <Controller
                          name="transportLicencePic"
                          control={control}
                          render={({ field }) => (
                            <Input
                              id="transportLicencePic"
                              type="file"
                              variant={"bordered"}
                              color="primary"
                              isDisabled={readStatus}
                              onChange={(e) => {
                                field.onChange(e.target.files);
                                handleImageChange(
                                  e.target.files,
                                  setTransportLicencePicPreview,
                                );
                              }}
                            />
                          )}
                        />
                      </div>
                    </div>
                  </div>
                </>
              )}
            </div>

            <DialogFooter>
              <Button
                type="submit"
                className="bg-slate-700 text-white dark:bg-slate-900"
                disabled={!isValid || isPending}
              >
                {isPending ? (
                  <Spinner className={"mr-1"} color={"white"} size={"sm"} />
                ) : null}
                Save changes
              </Button>
            </DialogFooter>
          </DialogContent>
        </form>
      </Dialog>

      {fullscreenImage && (
        <div
          className="fixed inset-0 z-[1000] flex items-center justify-center bg-black bg-opacity-70 backdrop-blur-sm pointer-events-auto"
          onClick={() => setFullscreenImage(null)}
        >
          <div className="relative z-[1500] max-w-lg max-h-xl p-4 pointer-events-auto">
            <button
              className="absolute translate-x-4 -translate-y-4 top-2 right-2 z-[2000] bg-black bg-opacity-50 text-white rounded-full p-1"
              onClick={(e) => {
                e.stopPropagation();
                setFullscreenImage(null);
              }}
            >
              <XIcon className="w-6 h-6" />
            </button>
            <Image
              src={fullscreenImage}
              alt="Fullscreen view"
              className="h-full w-full object-contain"
              onClick={(e) => {
                e.stopPropagation();
              }}
            />
          </div>
        </div>
      )}
    </>
  );
}
