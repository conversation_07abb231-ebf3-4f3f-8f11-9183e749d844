import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { SVGProps, useState } from "react";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import useGetAllRegisteredDrivers from "@/utils/hooks/drivers/get-all-registered-drivers.ts";
import { components } from "@/api-schema";
import PagePagination from "@/components/PagePagination.tsx";
import { DriverDialog } from "@/routes/_authenticated/drivers/-components/drivermodal.tsx";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { JSX } from "react/jsx-runtime";

export const Route = createFileRoute("/_authenticated/drivers/")({
  component: Drivers,
});

function Drivers() {
  const navigate = useNavigate();
  const [selectedColumns, setSelectedColumns] = useState([
    "name",
    "email",
    "address",
    "phone",
    "DOB",
    "status",
    "action",
  ]);

  const [currentPage, setCurrentPage] = useState(1);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedDriverId, setSelectedDriverId] = useState<string | null>(null);

  const handleColumnChange = (column: string) => {
    setSelectedColumns((prevSelectedColumns) =>
      prevSelectedColumns.includes(column)
        ? prevSelectedColumns.filter((col) => col !== column)
        : [...prevSelectedColumns, column],
    );
  };

  type Driver = components["schemas"]["DriverDto"];

  const {
    data: driversResponse,
    error,
    isLoading,
  } = useGetAllRegisteredDrivers({ page: currentPage });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error loading drivers: {error.message}</div>;

  const driverList: Driver[] = driversResponse?.data ?? [];

  const handleDriverUpdateSuccess = () =>
    toast.success("Driver updated successfully!");

  const handleViewDetails = (driverId: string) => {
    setSelectedDriverId(driverId);
    setIsDialogOpen(true);
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= driversResponse.meta.pageCount) {
      setCurrentPage(newPage);
    }
  };

  return (
    <div className="p-4 bg-inherit">
      <ToastContainer />
      <div className="flex justify-between items-center mb-4">
        <div className="relative">
          <div className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search"
            className="pl-8 sm:w-[300px] md:w-[200px] lg:w-[300px] bg-slate-200 dark:bg-slate-800"
          />
        </div>
        <div className="flex items-center gap-2">
          <Button
            className="ml-auto bg-slate-700 text-white dark:hover:bg-slate-800 hidden md:flex"
            onClick={() =>
              navigate({
                to: "/drivers/register-driver",
                search: {
                  email: undefined,
                  firstName: undefined,
                  lastName: undefined,
                },
              })
            }
          >
            Register Driver
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <SlidersHorizontalIcon className="h-4 w-4" />
                <span className="sr-only">Show columns</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px]">
              <div className="grid gap-2 p-4">
                {[
                  "name",
                  "email",
                  "address",
                  "phone",
                  "DOB",
                  "status",
                  "action",
                ].map((column) => (
                  <div className="flex items-center gap-2" key={column}>
                    <Switch
                      id={column}
                      checked={selectedColumns.includes(column)}
                      onCheckedChange={() => handleColumnChange(column)}
                    />
                    <Label htmlFor={column}>
                      {column.charAt(0).toUpperCase() +
                        column.slice(1).replace(/_/g, " ")}
                    </Label>
                  </div>
                ))}
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <Card className="bg-slate-200 dark:bg-slate-800 rounded-lg">
        <Table>
          <TableHeader className="rounded-lg">
            <TableRow className="hover:bg-slate-300 dark:bg-slate-900 bg-slate-300 dark:hover:bg-slate-900 rounded-lg">
              {selectedColumns.includes("name") && (
                <TableHead className="w-[200px] text-center rounded-l-lg">
                  Name
                </TableHead>
              )}
              {selectedColumns.includes("address") && (
                <TableHead className="w-[150px] text-center">Address</TableHead>
              )}
              {selectedColumns.includes("email") && (
                <TableHead className="w-[250px] text-center">Email</TableHead>
              )}
              {selectedColumns.includes("phone") && (
                <TableHead className="w-[180px] text-center">
                  Phone number
                </TableHead>
              )}
              {selectedColumns.includes("DOB") && (
                <TableHead className="w-[180px] text-center">
                  Date of Birth
                </TableHead>
              )}
              {selectedColumns.includes("status") && (
                <TableHead className="w-[120px] text-center">Status</TableHead>
              )}
              {selectedColumns.includes("action") && (
                <TableHead className="w-[150px] text-center rounded-r-lg">
                  Action
                </TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {driverList.map((driver) => (
              <TableRow
                key={driver.id}
                className="align-middle hover:shadow-background hover:cursor-pointer dark:hover:shadow-"
                onClick={() => handleViewDetails(driver.id)}
              >
                {selectedColumns.includes("name") && (
                  <TableCell className="w-[200px] text-center flex items-center">
                    <Avatar>
                      <AvatarImage src={driver.user?.profilePic} />
                      <AvatarFallback>
                        {driver.user?.firstName
                          ? driver.user?.firstName.charAt(0)
                          : ""}
                      </AvatarFallback>
                    </Avatar>
                    <span className="ml-2">
                      {driver.user?.firstName + " " + driver.user?.lastName}
                    </span>
                  </TableCell>
                )}
                {selectedColumns.includes("address") && (
                  <TableCell className="w-[150px] text-center overflow-hidden">
                    {driver.address}
                  </TableCell>
                )}
                {selectedColumns.includes("email") && (
                  <TableCell className="w-[250px] text-center">
                    {driver.user?.email}
                  </TableCell>
                )}
                {selectedColumns.includes("phone") && (
                  <TableCell className="w-[180px] text-center">
                    {driver.mobile}
                  </TableCell>
                )}
                {selectedColumns.includes("DOB") && (
                  <TableCell className="w-[180px] text-center">
                    {driver.dob
                      ? new Date(driver.dob).toLocaleDateString()
                      : ""}
                  </TableCell>
                )}
                {selectedColumns.includes("status") && (
                  <TableCell className="w-[120px] text-center">
                    <Badge
                      variant="secondary"
                      className={`${driver.status === "ACTIVE" ? "bg-green-500" : driver.status === "INACTIVE" ? "bg-red-500" : "bg-orange-400"} text-white`}
                    >
                      {driver.status}
                    </Badge>
                  </TableCell>
                )}
                {selectedColumns.includes("action") && (
                  <TableCell className="w-[150px] text-center">
                    <Button
                      variant="outline"
                      onClick={() => handleViewDetails(driver.id)}
                    >
                      View Details
                    </Button>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>

      {driversResponse.meta.pageCount > 1 && (
        <PagePagination
          meta={driversResponse?.meta}
          onPageChange={handlePageChange}
        />
      )}

      {selectedDriverId && (
        <DriverDialog
          driverId={selectedDriverId}
          isOpen={isDialogOpen}
          onClose={() => setIsDialogOpen(false)}
          onSuccess={() => {
            handleDriverUpdateSuccess();
            setIsDialogOpen(false);
          }}
        />
      )}
    </div>
  );
}

export default Drivers;

function SlidersHorizontalIcon(
  props: JSX.IntrinsicAttributes & SVGProps<SVGSVGElement>,
) {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <line x1="21" x2="14" y1="4" y2="4" />
      <line x1="10" x2="3" y1="4" y2="4" />
      <line x1="21" x2="12" y1="12" y2="12" />
      <line x1="8" x2="3" y1="12" y2="12" />
      <line x1="21" x2="16" y1="20" y2="20" />
      <line x1="12" x2="3" y1="20" y2="20" />
      <line x1="14" x2="14" y1="2" y2="6" />
      <line x1="8" x2="8" y1="10" y2="14" />
      <line x1="16" x2="16" y1="18" y2="22" />
    </svg>
  );
}
