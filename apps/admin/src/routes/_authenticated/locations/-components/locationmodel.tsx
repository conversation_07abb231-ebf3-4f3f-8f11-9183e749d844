import useGetLocationDetails from "@/utils/hooks/locations/get-location-details.ts";
import useUpdateLocationDetails from "@/utils/hooks/locations/use-update-location-details.ts";
import { Dialog, DialogContent, DialogTitle } from "@radix-ui/react-dialog";
import { AlertTriangleIcon, XIcon } from "lucide-react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { DialogFooter, DialogHeader } from "@/components/ui/dialog";
import { components } from "@/api-schema";
import { Label } from "@/components/ui/label";
import { But<PERSON>, Spinner, Input } from "@heroui/react";
import ConfirmationDialog from "@/components/confirmationDialog.tsx";

type LocationDto = components["schemas"]["UpdateLocationDto"];

interface LocationDialogProps {
  locationId: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const locationUpdateSchema = z.object({
  name: z.string().nonempty("Name is required"),
  description: z.string() || z.null(),
  capacity: z.number().min(1, "Capacity must be at least 1"),
  address: z.string() || z.null(),
  longitude: z
    .number()
    .min(-180, "Longitude must be at least -180")
    .max(180, "Longitude must be at most 180"),
  latitude: z
    .number()
    .min(-90, "Latitude must be at least -90")
    .max(90, "Latitude must be at most 90"),
  type: z.enum(["YARD", "MECHANICSHOP", "ILLEGAL_PARKING"]),
});

export function LocationDialog({
  locationId,
  isOpen,
  onClose,
  onSuccess,
}: LocationDialogProps) {
  const {
    data: locationDetails,
    error,
    isLoading,
  } = useGetLocationDetails(locationId);

  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingData, setPendingData] = useState<LocationDto | null>(null);

  const handleSaveClick = (data: LocationDto) => {
    setPendingData(data);
    setShowConfirmDialog(true);
  };

  const { register, formState, handleSubmit, reset } = useForm<LocationDto>({
    defaultValues: locationDetails
      ? {
          name: locationDetails.name,
          description: locationDetails.description,
          capacity: locationDetails.capacity,
          address: locationDetails.address,
          longitude: locationDetails.longitude,
          latitude: locationDetails.latitude,
          type: locationDetails.type as
            | "YARD"
            | "MECHANICSHOP"
            | "ILLEGAL_PARKING",
        }
      : undefined,
    resolver: zodResolver(locationUpdateSchema),
  });

  useEffect(() => {
    if (locationDetails) {
      const validType = ["YARD", "MECHANICSHOP", "ILLEGAL_PARKING"].includes(
        locationDetails.type,
      )
        ? (locationDetails.type as LocationDto["type"])
        : undefined;

      reset({
        ...locationDetails,
        type: validType,
      });
    }
  }, [locationDetails, reset]);

  const {
    mutate,
    isError,
    error: mutationError,
    isPending,
  } = useUpdateLocationDetails();

  const onSubmit = () => {
    if (!pendingData) return;

    setShowConfirmDialog(false);
    mutate(
      { locationId, location: pendingData },
      {
        onSuccess: () => {
          onSuccess();
          onClose();
        },
      }
    );
  };
  const [isEditing, setIsEditing] = useState(false);

  const handleEditing = () => {
    setIsEditing(true);
  };

  useEffect(() => {
    if (!isOpen) {
      setIsEditing(false);
    }
  }, [isOpen]);

  return (
    <>
      {isOpen && (
        <div className="fixed inset-0 z-40 bg-black bg-opacity-50 backdrop-blur-sm" />
      )}
      <Dialog open={isOpen} onOpenChange={onClose}>
        <form onSubmit={handleSubmit(handleSaveClick)}>
          <DialogContent className="fixed top-1/2 left-1/2 z-50 w-full max-w-screen-lg max-h-full transform -translate-x-1/2 -translate-y-1/2 bg-slate-50 dark:bg-slate-800 p-6 rounded-lg shadow-lg transition-transform duration-300 ease-out">
            <DialogHeader>
              <DialogTitle className="text-4xl font-bold text-left text-gray-800 dark:text-white border-b-2 border-gray-300 dark:border-gray-700 py-4">
                Location Details
              </DialogTitle>
              <button
                type="button"
                className="absolute top-4 right-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                onClick={onClose}
              >
                <XIcon className="w-6 h-6" />
              </button>
            </DialogHeader>
            <div className="grid grid-cols-2 gap-6 p-4 max-h-[70vh] overflow-y-auto">
              {isLoading && (
                <div className="col-span-2 text-center text-gray-700 dark:text-gray-300">
                  <p>Loading...</p>
                </div>
              )}
              {error && (
                <div className="col-span-2 text-center text-red-600 dark:text-red-400">
                  <AlertTriangleIcon className="w-6 h-6 inline mr-2" />
                  Error fetching location details.
                </div>
              )}
              {locationDetails && (
                <div className="col-span-2 flex flex-col space-y-4">
                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label
                      htmlFor="name"
                      className="text-gray-700 dark:text-gray-300"
                    >
                      Name
                    </Label>
                    <Input
                      isDisabled={!isEditing}
                      className={"border rounded-xl "}
                      variant={"bordered"}
                      {...register("name")}
                    />
                    {formState.errors.name && (
                      <span className="text-red-500 dark:text-red-400">
                        {formState.errors.name.message}
                      </span>
                    )}
                  </div>

                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label
                      htmlFor="description"
                      className="text-gray-700 dark:text-gray-300"
                    >
                      Description
                    </Label>
                    <Input
                      isDisabled={!isEditing}
                      className={"border rounded-xl "}
                      variant={"bordered"}
                      {...register("description")}
                    />
                    {formState.errors.description && (
                      <span className="text-red-500 dark:text-red-400">
                        {formState.errors.description.message}
                      </span>
                    )}
                  </div>

                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label
                      htmlFor="capacity"
                      className="text-gray-700 dark:text-gray-300"
                    >
                      Capacity
                    </Label>
                    <Input
                      isDisabled={!isEditing}
                      type="number"
                      className={"border rounded-xl "}
                      variant={"bordered"}
                      {...register("capacity", { valueAsNumber: true })}
                    />
                    {formState.errors.capacity && (
                      <span className="text-red-500 dark:text-red-400">
                        {formState.errors.capacity.message}
                      </span>
                    )}
                  </div>

                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label
                      htmlFor="address"
                      className="text-gray-700 dark:text-gray-300"
                    >
                      Address
                    </Label>
                    <Input
                      isDisabled={!isEditing}
                      className={"border rounded-xl "}
                      variant={"bordered"}
                      {...register("address")}
                    />
                    {formState.errors.address && (
                      <span className="text-red-500 dark:text-red-400">
                        {formState.errors.address.message}
                      </span>
                    )}
                  </div>

                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label
                      htmlFor="longitude"
                      className="text-gray-700 dark:text-gray-300"
                    >
                      Longitude
                    </Label>
                    <Input
                      isDisabled={!isEditing}
                      className={"border rounded-xl "}
                      variant={"bordered"}
                      type="number"
                      step="any"
                      {...register("longitude", { valueAsNumber: true })}
                    />
                    {formState.errors.longitude && (
                      <span className="text-red-500 dark:text-red-400">
                        {formState.errors.longitude.message}
                      </span>
                    )}
                  </div>

                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label
                      htmlFor="latitude"
                      className="text-gray-700 dark:text-gray-300"
                    >
                      Latitude
                    </Label>
                    <Input
                      isDisabled={!isEditing}
                      className={"border rounded-xl "}
                      variant={"bordered"}
                      type="number"
                      step="any"
                      {...register("latitude", { valueAsNumber: true })}
                    />
                    {formState.errors.latitude && (
                      <span className="text-red-500 dark:text-red-400">
                        {formState.errors.latitude.message}
                      </span>
                    )}
                  </div>

                  <div className="grid w-full max-w-sm items-center gap-1.5">
                    <Label
                      htmlFor="type"
                      className="text-gray-700 dark:text-gray-300"
                    >
                      Type
                    </Label>
                    <select
                      {...register("type")}
                      disabled={!isEditing}
                      className="border rounded-xl shadow-background border-gray-300 dark:border-gray-600 p-2 bg-inherit dark:bg-gray-700 text-gray-800 dark:text-gray-200"
                    >
                      <option value={"YARD"}>YARD</option>
                      <option value={"MECHANICSHOP"}>MECHANICSHOP</option>
                      <option value={"ILLEGAL_PARKING"}>ILLEGAL_PARKING</option>
                    </select>
                    {formState.errors.latitude && (
                      <span className="text-red-500 dark:text-red-400">
                        {formState.errors.latitude.message}
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>

            {isError && (
              <div className="text-red-600 dark:text-red-400 text-center">
                Error updating location details: {mutationError?.message}
              </div>
            )}

            <DialogFooter>
              <Button
                type="button"
                radius={"lg"}
                className={`${isEditing ? "hidden" : ""} bg-slate-700 text-white   dark:bg-slate-900`}
                onPress={handleEditing}
              >
                Enable Editing Mode
              </Button>

              <Button
                type="submit"
                className={`${isEditing ? "" : "hidden"} bg-slate-700 text-white dark:bg-slate-900`}
              >
                {isPending ? (
                  <Spinner className={"mr-1"} color={"white"} size={"sm"} />
                ) : (
                  ""
                )}
                Save changes
              </Button>
            </DialogFooter>
          </DialogContent>
        </form>
      </Dialog>

      <ConfirmationDialog
        isOpen={showConfirmDialog}
        message="Are you sure you want to save changes?"
        onConfirm={onSubmit}
        onCancel={() => setShowConfirmDialog(false)}
      />
    </>
  );
}

export default LocationDialog;
