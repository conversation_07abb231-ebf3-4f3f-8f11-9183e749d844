import { createFileRoute } from "@tanstack/react-router";
import { But<PERSON>, Input, Spinner } from "@heroui/react";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Controller, SubmitHandler, useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import useRegisterLocation from "@/utils/hooks/locations/use-register-location.ts";

// Define Zod schema for validation
const locationRegistrationSchema = z.object({
  name: z.string().nonempty("Location name is required"),
  description: z.string().nonempty("Description is required"),
  address: z.string().nonempty("Address is required"),
  longitude: z
    .number()
    .min(-180, "Longitude must be at least -180")
    .max(180, "Longitude must be at most 180"),
  latitude: z
    .number()
    .min(-90, "Latitude must be at least -90")
    .max(90, "Latitude must be at most 90"),
  capacity: z.number().min(1, "Capacity must be at least 1"),
  type: z.enum(["YARD", "MECHANICSHOP", "ILLEGAL_PARKING"]),
});

export type LocationRegistrationFormValues = z.infer<
  typeof locationRegistrationSchema
>;

export const Route = createFileRoute(
  "/_authenticated/locations/register-location",
)({
  component: RegisterLocation,
});

function RegisterLocation() {
  const { mutate: registerLocation, isPending } = useRegisterLocation();
  const {
    handleSubmit,
    control,
    formState: { errors, isValid },
    reset,
  } = useForm<LocationRegistrationFormValues>({
    resolver: zodResolver(locationRegistrationSchema),
    mode: "onChange",
    defaultValues: {
      name: "",
      description: "",
      address: "",
      longitude: undefined,
      latitude: undefined,
      capacity: 100,
      type: "YARD", // Assuming type is fixed as "YARD" todo: fix this
    },
  });

  const onSubmit: SubmitHandler<LocationRegistrationFormValues> = async (
    data,
  ) => {
    try {
      const registrationData = {
        name: data.name,
        description: data.description,
        address: data.address,
        longitude: data.longitude,
        latitude: data.latitude,
        capacity: data.capacity,
        type: data.type,
      };

      console.log("Submitting registration data:", registrationData);

      registerLocation(registrationData, {
        onSuccess: () => {
          toast.success("Location registered successfully!");
          reset({
            name: "",
            description: "",
            address: "",
            longitude: undefined,
            latitude: undefined,
            capacity: 100,
          });
        },
        onError: (error) => {
          console.error("Error registering location:", error);
          toast.error("Failed to register location.");
        },
      });
    } catch (error) {
      console.error("Unexpected error:", error);
      toast.error("An unexpected error occurred. Please try again.");
    }
  };

  return (
    <div className="flex flex-col min-h-screen bg-inherit">
      <ToastContainer />
      <Card className="flex-1 grid gap-6 p-4 md:gap-8 md:p-6 md:pt-0 bg-inherit border-0">
        <CardContent>
          <div className={" pb-20"}>
            Please fill out the form below to complete your location
            registration.
          </div>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex flex-col gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="name">Location Name</Label>
                  <Controller
                    name="name"
                    control={control}
                    render={({ field }) => (
                      <Input
                        id="name"
                        placeholder="Enter location name"
                        isRequired
                        variant="bordered"
                        isInvalid={!!errors.name}
                        errorMessage={errors.name?.message}
                        {...field}
                      />
                    )}
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Controller
                    name="description"
                    control={control}
                    render={({ field }) => (
                      <Input
                        id="description"
                        placeholder="Enter description"
                        isRequired
                        variant="bordered"
                        isInvalid={!!errors.description}
                        errorMessage={errors.description?.message}
                        {...field}
                      />
                    )}
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="address">Address</Label>
                  <Controller
                    name="address"
                    control={control}
                    render={({ field }) => (
                      <Input
                        id="address"
                        placeholder="Enter address"
                        isRequired
                        variant="bordered"
                        isInvalid={!!errors.address}
                        errorMessage={errors.address?.message}
                        {...field}
                      />
                    )}
                  />
                </div>
              </div>

              <div className="flex flex-col gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="longitude">Longitude</Label>
                  <Controller
                    name="longitude"
                    control={control}
                    render={({ field }) => (
                      <Input
                        id="longitude"
                        placeholder="Enter longitude"
                        type="number"
                        isRequired
                        variant="bordered"
                        isInvalid={!!errors.longitude}
                        errorMessage={errors.longitude?.message}
                        value={
                          field.value !== undefined ? String(field.value) : ""
                        }
                        onChange={(e) => field.onChange(e.target.valueAsNumber)}
                      />
                    )}
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="latitude">Latitude</Label>
                  <Controller
                    name="latitude"
                    control={control}
                    render={({ field }) => (
                      <Input
                        id="latitude"
                        placeholder="Enter latitude"
                        type="number"
                        isRequired
                        variant="bordered"
                        isInvalid={!!errors.latitude}
                        errorMessage={errors.latitude?.message}
                        value={
                          field.value !== undefined ? String(field.value) : ""
                        }
                        onChange={(e) => field.onChange(e.target.valueAsNumber)}
                      />
                    )}
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="capacity">Capacity</Label>
                  <Controller
                    name="capacity"
                    control={control}
                    render={({ field }) => (
                      <Input
                        id="capacity"
                        placeholder="Enter capacity"
                        type="number"
                        isRequired
                        variant="bordered"
                        isInvalid={!!errors.capacity}
                        errorMessage={errors.capacity?.message}
                        value={
                          field.value !== undefined ? String(field.value) : ""
                        }
                        onChange={(e) => field.onChange(e.target.valueAsNumber)}
                      />
                    )}
                  />
                </div>
              </div>
            </div>
            <div className="w-full md:w-fit px-4 md:ml-auto">
              <Button
                type="submit"
                disabled={!isValid}
                className="font-semibold w-full md:w-fit mt-10"
              >
                {isPending ? (
                  <Spinner className={"mr-1"} color={"white"} size={"sm"} />
                ) : (
                  ""
                )}
                Register Location
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}

export default RegisterLocation;
