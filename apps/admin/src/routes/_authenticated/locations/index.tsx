import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import useGetAllRegisteredLocations from "@/utils/hooks/locations/get-all-registered-locations.ts";
import { components } from "@/api-schema";
import { SlidersHorizontal } from "lucide-react";
import { LocationDialog } from "@/routes/_authenticated/locations/-components/locationmodel.tsx";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import PagePagination from "@/components/PagePagination.tsx";

export const Route = createFileRoute("/_authenticated/locations/")({
  component: Locations,
});

function Locations() {
  const navigate = useNavigate();
  const [selectedColumns, setSelectedColumns] = useState([
    "name",
    "description",
    "capacity",
    "address",
    "action",
  ]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedLocationId, setSelectedLocationId] = useState<string | null>(
    null,
  );
  const [currentPage, setCurrentPage] = useState(1);

  const handleColumnChange = (column: string) => {
    setSelectedColumns((prevSelectedColumns) =>
      prevSelectedColumns.includes(column)
        ? prevSelectedColumns.filter((col) => col !== column)
        : [...prevSelectedColumns, column],
    );
  };

  type Location = components["schemas"]["LocationDto"];

  const {
    data: locationsResponse,
    error,
    isLoading,
  } = useGetAllRegisteredLocations({
    page: currentPage,
  });

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  const locationList: Location[] = locationsResponse?.data ?? [];

  const handleLocationUpdateSuccess = () => {
    toast.success("Location updated successfully!");
  };

  const handleViewDetails = (locationId: string) => {
    setSelectedLocationId(locationId);
    setIsDialogOpen(true);
  };

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= locationsResponse?.meta?.pageCount) {
      setCurrentPage(newPage);
    }
  };

  return (
    <div className="p-4 bg-inherit">
      <ToastContainer />
      <div className="flex justify-between items-center mb-4 ">
        <div className="relative">
          <Input
            type="search"
            placeholder="Search"
            className="pl-8 sm:w-[300px] md:w-[200px] lg:w-[300px] bg-slate-200 dark:bg-slate-800"
          />
        </div>
        <div className="flex items-center gap-2">
          <Button
            className="ml-auto bg-slate-700 text-white dark:hover:bg-slate-800 hidden md:flex"
            variant={"default"}
            onClick={() => navigate({ to: "/locations/register-location" })}
          >
            Register Location
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <SlidersHorizontal className="h-4 w-4" />
                <span className="sr-only">Show columns</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[200px]">
              <div className="grid gap-2 p-4">
                {["name", "description", "capacity", "address", "action"].map(
                  (column) => (
                    <div className="flex items-center gap-2" key={column}>
                      <Switch
                        id={column}
                        checked={selectedColumns.includes(column)}
                        onCheckedChange={() => handleColumnChange(column)}
                      />
                      <Label htmlFor={column}>
                        {column.charAt(0).toUpperCase() +
                          column.slice(1).replace(/_/g, " ")}
                      </Label>
                    </div>
                  ),
                )}
              </div>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <Card className={"bg-slate-200 dark:bg-slate-800 rounded-lg"}>
        <Table>
          <TableHeader className={"rounded-lg"}>
            <TableRow className="bg-slate-300 dark:bg-slate-900 rounded-lg">
              {selectedColumns.includes("name") && (
                <TableHead className="w-[200px] text-center">Name</TableHead>
              )}
              {selectedColumns.includes("description") && (
                <TableHead className="w-[250px] text-center">
                  Description
                </TableHead>
              )}
              {selectedColumns.includes("capacity") && (
                <TableHead className="w-[120px] text-center">
                  Capacity
                </TableHead>
              )}
              {selectedColumns.includes("address") && (
                <TableHead className="w-[200px] text-center">Address</TableHead>
              )}
              {selectedColumns.includes("action") && (
                <TableHead className="w-[150px] text-center">Action</TableHead>
              )}
            </TableRow>
          </TableHeader>
          <TableBody>
            {locationList.map((location) => (
              <TableRow
                key={location.id}
                className={"hover:cursor-pointer"}
                onClick={() => handleViewDetails(location.id)}
              >
                {selectedColumns.includes("name") && (
                  <TableCell className="text-center">{location.name}</TableCell>
                )}
                {selectedColumns.includes("description") && (
                  <TableCell className="text-center">
                    {location.description}
                  </TableCell>
                )}
                {selectedColumns.includes("capacity") && (
                  <TableCell className="text-center">
                    {location.capacity}
                  </TableCell>
                )}
                {selectedColumns.includes("address") && (
                  <TableCell className="text-center">
                    {location.address}
                  </TableCell>
                )}
                {selectedColumns.includes("action") && (
                  <TableCell className="text-center">
                    <Button variant="outline">View Details</Button>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>
      {locationsResponse.meta?.pageCount > 1 && (
        <PagePagination
          meta={locationsResponse.meta}
          onPageChange={handlePageChange}
        />
      )}
      {selectedLocationId && (
        <LocationDialog
          locationId={selectedLocationId}
          isOpen={isDialogOpen}
          onClose={() => setIsDialogOpen(false)}
          onSuccess={handleLocationUpdateSuccess}
        />
      )}
    </div>
  );
}

export default Locations;
