import { useEffect, useRef } from "react";
import { createRootRoute, Outlet, useNavigate } from "@tanstack/react-router";
import { TanStackRouterDevtools } from "@tanstack/react-router-devtools";
import { auth } from "@/auth/auth.ts";
import { useAuth } from "@/auth/use-auth.ts";
import { User } from "firebase/auth";

export const Route = createRootRoute({
  component: RootLayout,
  beforeLoad: () => {
    console.log("Root beforeLoad", !!auth.currentUser);
    return { user: auth.currentUser };
  },
});

function RootLayout() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const previousUserRef = useRef<User | undefined>(user);

  useEffect(() => {
    // Todo : Culprit of the infinite loop of redirection to the Dashboard page
    const previousUser = previousUserRef.current;
    if (previousUser !== user) {
      console.log("User changed:", { previousUser, newUser: user });
      navigate({ to: "/" });
    }
  }, [navigate, user]);

  return (
    <>
      <Outlet />
      <TanStackRouterDevtools position="bottom-right" initialIsOpen={false} />
    </>
  );
}
