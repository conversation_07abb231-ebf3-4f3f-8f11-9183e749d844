/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from './routes/__root'
import { Route as AuthenticatedImport } from './routes/_authenticated'
import { Route as AuthImport } from './routes/_auth'
import { Route as AuthLoginImport } from './routes/_auth/login'
import { Route as AuthenticatedUsersIndexImport } from './routes/_authenticated/users/index'
import { Route as AuthenticatedTrucksIndexImport } from './routes/_authenticated/trucks/index'
import { Route as AuthenticatedTripsIndexImport } from './routes/_authenticated/trips/index'
import { Route as AuthenticatedLocationsIndexImport } from './routes/_authenticated/locations/index'
import { Route as AuthenticatedDriversIndexImport } from './routes/_authenticated/drivers/index'
import { Route as AuthenticatedClientsIndexImport } from './routes/_authenticated/clients/index'
import { Route as AuthenticatedDashboardIndexImport } from './routes/_authenticated/_dashboard/index'
import { Route as AuthenticatedTrucksRegisterTruckImport } from './routes/_authenticated/trucks/register-truck'
import { Route as AuthenticatedSettingsRoadAssistanceChargesImport } from './routes/_authenticated/settings/road-assistance-charges'
import { Route as AuthenticatedSettingsProfileImport } from './routes/_authenticated/settings/profile'
import { Route as AuthenticatedSettingsIllegalParkingChargesImport } from './routes/_authenticated/settings/illegal-parking-charges'
import { Route as AuthenticatedSettingsDestinationChargesImport } from './routes/_authenticated/settings/destination-charges'
import { Route as AuthenticatedLocationsRegisterLocationImport } from './routes/_authenticated/locations/register-location'
import { Route as AuthenticatedDriversRegisterDriverImport } from './routes/_authenticated/drivers/register-driver'

// Create/Update Routes

const AuthenticatedRoute = AuthenticatedImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRoute,
} as any)

const AuthRoute = AuthImport.update({
  id: '/_auth',
  getParentRoute: () => rootRoute,
} as any)

const AuthLoginRoute = AuthLoginImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => AuthRoute,
} as any)

const AuthenticatedUsersIndexRoute = AuthenticatedUsersIndexImport.update({
  id: '/users/',
  path: '/users/',
  getParentRoute: () => AuthenticatedRoute,
} as any)

const AuthenticatedTrucksIndexRoute = AuthenticatedTrucksIndexImport.update({
  id: '/trucks/',
  path: '/trucks/',
  getParentRoute: () => AuthenticatedRoute,
} as any)

const AuthenticatedTripsIndexRoute = AuthenticatedTripsIndexImport.update({
  id: '/trips/',
  path: '/trips/',
  getParentRoute: () => AuthenticatedRoute,
} as any)

const AuthenticatedLocationsIndexRoute =
  AuthenticatedLocationsIndexImport.update({
    id: '/locations/',
    path: '/locations/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticatedDriversIndexRoute = AuthenticatedDriversIndexImport.update({
  id: '/drivers/',
  path: '/drivers/',
  getParentRoute: () => AuthenticatedRoute,
} as any)

const AuthenticatedClientsIndexRoute = AuthenticatedClientsIndexImport.update({
  id: '/clients/',
  path: '/clients/',
  getParentRoute: () => AuthenticatedRoute,
} as any)

const AuthenticatedDashboardIndexRoute =
  AuthenticatedDashboardIndexImport.update({
    id: '/_dashboard/',
    path: '/',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticatedTrucksRegisterTruckRoute =
  AuthenticatedTrucksRegisterTruckImport.update({
    id: '/trucks/register-truck',
    path: '/trucks/register-truck',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticatedSettingsRoadAssistanceChargesRoute =
  AuthenticatedSettingsRoadAssistanceChargesImport.update({
    id: '/settings/road-assistance-charges',
    path: '/settings/road-assistance-charges',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticatedSettingsProfileRoute =
  AuthenticatedSettingsProfileImport.update({
    id: '/settings/profile',
    path: '/settings/profile',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticatedSettingsIllegalParkingChargesRoute =
  AuthenticatedSettingsIllegalParkingChargesImport.update({
    id: '/settings/illegal-parking-charges',
    path: '/settings/illegal-parking-charges',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticatedSettingsDestinationChargesRoute =
  AuthenticatedSettingsDestinationChargesImport.update({
    id: '/settings/destination-charges',
    path: '/settings/destination-charges',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticatedLocationsRegisterLocationRoute =
  AuthenticatedLocationsRegisterLocationImport.update({
    id: '/locations/register-location',
    path: '/locations/register-location',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

const AuthenticatedDriversRegisterDriverRoute =
  AuthenticatedDriversRegisterDriverImport.update({
    id: '/drivers/register-driver',
    path: '/drivers/register-driver',
    getParentRoute: () => AuthenticatedRoute,
  } as any)

// Populate the FileRoutesByPath interface

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/_auth': {
      id: '/_auth'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthImport
      parentRoute: typeof rootRoute
    }
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedImport
      parentRoute: typeof rootRoute
    }
    '/_auth/login': {
      id: '/_auth/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof AuthLoginImport
      parentRoute: typeof AuthImport
    }
    '/_authenticated/drivers/register-driver': {
      id: '/_authenticated/drivers/register-driver'
      path: '/drivers/register-driver'
      fullPath: '/drivers/register-driver'
      preLoaderRoute: typeof AuthenticatedDriversRegisterDriverImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/locations/register-location': {
      id: '/_authenticated/locations/register-location'
      path: '/locations/register-location'
      fullPath: '/locations/register-location'
      preLoaderRoute: typeof AuthenticatedLocationsRegisterLocationImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/settings/destination-charges': {
      id: '/_authenticated/settings/destination-charges'
      path: '/settings/destination-charges'
      fullPath: '/settings/destination-charges'
      preLoaderRoute: typeof AuthenticatedSettingsDestinationChargesImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/settings/illegal-parking-charges': {
      id: '/_authenticated/settings/illegal-parking-charges'
      path: '/settings/illegal-parking-charges'
      fullPath: '/settings/illegal-parking-charges'
      preLoaderRoute: typeof AuthenticatedSettingsIllegalParkingChargesImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/settings/profile': {
      id: '/_authenticated/settings/profile'
      path: '/settings/profile'
      fullPath: '/settings/profile'
      preLoaderRoute: typeof AuthenticatedSettingsProfileImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/settings/road-assistance-charges': {
      id: '/_authenticated/settings/road-assistance-charges'
      path: '/settings/road-assistance-charges'
      fullPath: '/settings/road-assistance-charges'
      preLoaderRoute: typeof AuthenticatedSettingsRoadAssistanceChargesImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/trucks/register-truck': {
      id: '/_authenticated/trucks/register-truck'
      path: '/trucks/register-truck'
      fullPath: '/trucks/register-truck'
      preLoaderRoute: typeof AuthenticatedTrucksRegisterTruckImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/_dashboard/': {
      id: '/_authenticated/_dashboard/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthenticatedDashboardIndexImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/clients/': {
      id: '/_authenticated/clients/'
      path: '/clients'
      fullPath: '/clients'
      preLoaderRoute: typeof AuthenticatedClientsIndexImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/drivers/': {
      id: '/_authenticated/drivers/'
      path: '/drivers'
      fullPath: '/drivers'
      preLoaderRoute: typeof AuthenticatedDriversIndexImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/locations/': {
      id: '/_authenticated/locations/'
      path: '/locations'
      fullPath: '/locations'
      preLoaderRoute: typeof AuthenticatedLocationsIndexImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/trips/': {
      id: '/_authenticated/trips/'
      path: '/trips'
      fullPath: '/trips'
      preLoaderRoute: typeof AuthenticatedTripsIndexImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/trucks/': {
      id: '/_authenticated/trucks/'
      path: '/trucks'
      fullPath: '/trucks'
      preLoaderRoute: typeof AuthenticatedTrucksIndexImport
      parentRoute: typeof AuthenticatedImport
    }
    '/_authenticated/users/': {
      id: '/_authenticated/users/'
      path: '/users'
      fullPath: '/users'
      preLoaderRoute: typeof AuthenticatedUsersIndexImport
      parentRoute: typeof AuthenticatedImport
    }
  }
}

// Create and export the route tree

interface AuthRouteChildren {
  AuthLoginRoute: typeof AuthLoginRoute
}

const AuthRouteChildren: AuthRouteChildren = {
  AuthLoginRoute: AuthLoginRoute,
}

const AuthRouteWithChildren = AuthRoute._addFileChildren(AuthRouteChildren)

interface AuthenticatedRouteChildren {
  AuthenticatedDriversRegisterDriverRoute: typeof AuthenticatedDriversRegisterDriverRoute
  AuthenticatedLocationsRegisterLocationRoute: typeof AuthenticatedLocationsRegisterLocationRoute
  AuthenticatedSettingsDestinationChargesRoute: typeof AuthenticatedSettingsDestinationChargesRoute
  AuthenticatedSettingsIllegalParkingChargesRoute: typeof AuthenticatedSettingsIllegalParkingChargesRoute
  AuthenticatedSettingsProfileRoute: typeof AuthenticatedSettingsProfileRoute
  AuthenticatedSettingsRoadAssistanceChargesRoute: typeof AuthenticatedSettingsRoadAssistanceChargesRoute
  AuthenticatedTrucksRegisterTruckRoute: typeof AuthenticatedTrucksRegisterTruckRoute
  AuthenticatedDashboardIndexRoute: typeof AuthenticatedDashboardIndexRoute
  AuthenticatedClientsIndexRoute: typeof AuthenticatedClientsIndexRoute
  AuthenticatedDriversIndexRoute: typeof AuthenticatedDriversIndexRoute
  AuthenticatedLocationsIndexRoute: typeof AuthenticatedLocationsIndexRoute
  AuthenticatedTripsIndexRoute: typeof AuthenticatedTripsIndexRoute
  AuthenticatedTrucksIndexRoute: typeof AuthenticatedTrucksIndexRoute
  AuthenticatedUsersIndexRoute: typeof AuthenticatedUsersIndexRoute
}

const AuthenticatedRouteChildren: AuthenticatedRouteChildren = {
  AuthenticatedDriversRegisterDriverRoute:
    AuthenticatedDriversRegisterDriverRoute,
  AuthenticatedLocationsRegisterLocationRoute:
    AuthenticatedLocationsRegisterLocationRoute,
  AuthenticatedSettingsDestinationChargesRoute:
    AuthenticatedSettingsDestinationChargesRoute,
  AuthenticatedSettingsIllegalParkingChargesRoute:
    AuthenticatedSettingsIllegalParkingChargesRoute,
  AuthenticatedSettingsProfileRoute: AuthenticatedSettingsProfileRoute,
  AuthenticatedSettingsRoadAssistanceChargesRoute:
    AuthenticatedSettingsRoadAssistanceChargesRoute,
  AuthenticatedTrucksRegisterTruckRoute: AuthenticatedTrucksRegisterTruckRoute,
  AuthenticatedDashboardIndexRoute: AuthenticatedDashboardIndexRoute,
  AuthenticatedClientsIndexRoute: AuthenticatedClientsIndexRoute,
  AuthenticatedDriversIndexRoute: AuthenticatedDriversIndexRoute,
  AuthenticatedLocationsIndexRoute: AuthenticatedLocationsIndexRoute,
  AuthenticatedTripsIndexRoute: AuthenticatedTripsIndexRoute,
  AuthenticatedTrucksIndexRoute: AuthenticatedTrucksIndexRoute,
  AuthenticatedUsersIndexRoute: AuthenticatedUsersIndexRoute,
}

const AuthenticatedRouteWithChildren = AuthenticatedRoute._addFileChildren(
  AuthenticatedRouteChildren,
)

export interface FileRoutesByFullPath {
  '': typeof AuthenticatedRouteWithChildren
  '/login': typeof AuthLoginRoute
  '/drivers/register-driver': typeof AuthenticatedDriversRegisterDriverRoute
  '/locations/register-location': typeof AuthenticatedLocationsRegisterLocationRoute
  '/settings/destination-charges': typeof AuthenticatedSettingsDestinationChargesRoute
  '/settings/illegal-parking-charges': typeof AuthenticatedSettingsIllegalParkingChargesRoute
  '/settings/profile': typeof AuthenticatedSettingsProfileRoute
  '/settings/road-assistance-charges': typeof AuthenticatedSettingsRoadAssistanceChargesRoute
  '/trucks/register-truck': typeof AuthenticatedTrucksRegisterTruckRoute
  '/': typeof AuthenticatedDashboardIndexRoute
  '/clients': typeof AuthenticatedClientsIndexRoute
  '/drivers': typeof AuthenticatedDriversIndexRoute
  '/locations': typeof AuthenticatedLocationsIndexRoute
  '/trips': typeof AuthenticatedTripsIndexRoute
  '/trucks': typeof AuthenticatedTrucksIndexRoute
  '/users': typeof AuthenticatedUsersIndexRoute
}

export interface FileRoutesByTo {
  '': typeof AuthRouteWithChildren
  '/login': typeof AuthLoginRoute
  '/drivers/register-driver': typeof AuthenticatedDriversRegisterDriverRoute
  '/locations/register-location': typeof AuthenticatedLocationsRegisterLocationRoute
  '/settings/destination-charges': typeof AuthenticatedSettingsDestinationChargesRoute
  '/settings/illegal-parking-charges': typeof AuthenticatedSettingsIllegalParkingChargesRoute
  '/settings/profile': typeof AuthenticatedSettingsProfileRoute
  '/settings/road-assistance-charges': typeof AuthenticatedSettingsRoadAssistanceChargesRoute
  '/trucks/register-truck': typeof AuthenticatedTrucksRegisterTruckRoute
  '/': typeof AuthenticatedDashboardIndexRoute
  '/clients': typeof AuthenticatedClientsIndexRoute
  '/drivers': typeof AuthenticatedDriversIndexRoute
  '/locations': typeof AuthenticatedLocationsIndexRoute
  '/trips': typeof AuthenticatedTripsIndexRoute
  '/trucks': typeof AuthenticatedTrucksIndexRoute
  '/users': typeof AuthenticatedUsersIndexRoute
}

export interface FileRoutesById {
  __root__: typeof rootRoute
  '/_auth': typeof AuthRouteWithChildren
  '/_authenticated': typeof AuthenticatedRouteWithChildren
  '/_auth/login': typeof AuthLoginRoute
  '/_authenticated/drivers/register-driver': typeof AuthenticatedDriversRegisterDriverRoute
  '/_authenticated/locations/register-location': typeof AuthenticatedLocationsRegisterLocationRoute
  '/_authenticated/settings/destination-charges': typeof AuthenticatedSettingsDestinationChargesRoute
  '/_authenticated/settings/illegal-parking-charges': typeof AuthenticatedSettingsIllegalParkingChargesRoute
  '/_authenticated/settings/profile': typeof AuthenticatedSettingsProfileRoute
  '/_authenticated/settings/road-assistance-charges': typeof AuthenticatedSettingsRoadAssistanceChargesRoute
  '/_authenticated/trucks/register-truck': typeof AuthenticatedTrucksRegisterTruckRoute
  '/_authenticated/_dashboard/': typeof AuthenticatedDashboardIndexRoute
  '/_authenticated/clients/': typeof AuthenticatedClientsIndexRoute
  '/_authenticated/drivers/': typeof AuthenticatedDriversIndexRoute
  '/_authenticated/locations/': typeof AuthenticatedLocationsIndexRoute
  '/_authenticated/trips/': typeof AuthenticatedTripsIndexRoute
  '/_authenticated/trucks/': typeof AuthenticatedTrucksIndexRoute
  '/_authenticated/users/': typeof AuthenticatedUsersIndexRoute
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | ''
    | '/login'
    | '/drivers/register-driver'
    | '/locations/register-location'
    | '/settings/destination-charges'
    | '/settings/illegal-parking-charges'
    | '/settings/profile'
    | '/settings/road-assistance-charges'
    | '/trucks/register-truck'
    | '/'
    | '/clients'
    | '/drivers'
    | '/locations'
    | '/trips'
    | '/trucks'
    | '/users'
  fileRoutesByTo: FileRoutesByTo
  to:
    | ''
    | '/login'
    | '/drivers/register-driver'
    | '/locations/register-location'
    | '/settings/destination-charges'
    | '/settings/illegal-parking-charges'
    | '/settings/profile'
    | '/settings/road-assistance-charges'
    | '/trucks/register-truck'
    | '/'
    | '/clients'
    | '/drivers'
    | '/locations'
    | '/trips'
    | '/trucks'
    | '/users'
  id:
    | '__root__'
    | '/_auth'
    | '/_authenticated'
    | '/_auth/login'
    | '/_authenticated/drivers/register-driver'
    | '/_authenticated/locations/register-location'
    | '/_authenticated/settings/destination-charges'
    | '/_authenticated/settings/illegal-parking-charges'
    | '/_authenticated/settings/profile'
    | '/_authenticated/settings/road-assistance-charges'
    | '/_authenticated/trucks/register-truck'
    | '/_authenticated/_dashboard/'
    | '/_authenticated/clients/'
    | '/_authenticated/drivers/'
    | '/_authenticated/locations/'
    | '/_authenticated/trips/'
    | '/_authenticated/trucks/'
    | '/_authenticated/users/'
  fileRoutesById: FileRoutesById
}

export interface RootRouteChildren {
  AuthRoute: typeof AuthRouteWithChildren
  AuthenticatedRoute: typeof AuthenticatedRouteWithChildren
}

const rootRouteChildren: RootRouteChildren = {
  AuthRoute: AuthRouteWithChildren,
  AuthenticatedRoute: AuthenticatedRouteWithChildren,
}

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_auth",
        "/_authenticated"
      ]
    },
    "/_auth": {
      "filePath": "_auth.tsx",
      "children": [
        "/_auth/login"
      ]
    },
    "/_authenticated": {
      "filePath": "_authenticated.tsx",
      "children": [
        "/_authenticated/drivers/register-driver",
        "/_authenticated/locations/register-location",
        "/_authenticated/settings/destination-charges",
        "/_authenticated/settings/illegal-parking-charges",
        "/_authenticated/settings/profile",
        "/_authenticated/settings/road-assistance-charges",
        "/_authenticated/trucks/register-truck",
        "/_authenticated/_dashboard/",
        "/_authenticated/clients/",
        "/_authenticated/drivers/",
        "/_authenticated/locations/",
        "/_authenticated/trips/",
        "/_authenticated/trucks/",
        "/_authenticated/users/"
      ]
    },
    "/_auth/login": {
      "filePath": "_auth/login.tsx",
      "parent": "/_auth"
    },
    "/_authenticated/drivers/register-driver": {
      "filePath": "_authenticated/drivers/register-driver.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/locations/register-location": {
      "filePath": "_authenticated/locations/register-location.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/settings/destination-charges": {
      "filePath": "_authenticated/settings/destination-charges.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/settings/illegal-parking-charges": {
      "filePath": "_authenticated/settings/illegal-parking-charges.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/settings/profile": {
      "filePath": "_authenticated/settings/profile.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/settings/road-assistance-charges": {
      "filePath": "_authenticated/settings/road-assistance-charges.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/trucks/register-truck": {
      "filePath": "_authenticated/trucks/register-truck.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/_dashboard/": {
      "filePath": "_authenticated/_dashboard/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/clients/": {
      "filePath": "_authenticated/clients/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/drivers/": {
      "filePath": "_authenticated/drivers/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/locations/": {
      "filePath": "_authenticated/locations/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/trips/": {
      "filePath": "_authenticated/trips/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/trucks/": {
      "filePath": "_authenticated/trucks/index.tsx",
      "parent": "/_authenticated"
    },
    "/_authenticated/users/": {
      "filePath": "_authenticated/users/index.tsx",
      "parent": "/_authenticated"
    }
  }
}
ROUTE_MANIFEST_END */
