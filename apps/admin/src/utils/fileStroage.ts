import { storage } from "@/auth/auth.ts";
import { getDownloadURL, ref, uploadBytes } from "firebase/storage";

export function useStorage() {
  async function uploadFile(file: File | undefined): Promise<string> {
    if (!file) {
      return "";
    }
    console.log(file.name + " " + file.type);
    if (
      !["image/png", "image/jpeg", "image/jpg", "image/png"].includes(file.type)
    ) {
      throw new Error("Invalid file type");
    }
    // add some uuid to the file name to make it unique in the storage
    const fileName: string =
      Math.random().toString(36).substring(7) +
      "-" +
      file.name +
      "-" +
      Date.now();
    const storageRef = ref(storage, `files/${fileName}`);
    await uploadBytes(storageRef, file);
    return await getDownloadURL(storageRef);
  }

  async function downloadFile(url: string) {
    const storageRef = ref(storage, url);
    return await getDownloadURL(storageRef);
  }

  return {
    uploadFile,
    downloadFile,
  };
}
