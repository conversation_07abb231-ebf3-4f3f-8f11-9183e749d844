import createClient from "openapi-fetch";
import { paths } from "@/api-schema";

export class Api {
  private constructor() {}

  private static _instance: ReturnType<typeof createClient<paths>>;

  static get instance() {
    if (!Api._instance) {
      const isProd = import.meta.env.PROD;
      const isMock = import.meta.env.VITE_MOCK_API?.toLowerCase() === "true";
      const _baseUrl = import.meta.env.VITE_API_URL as string;

      const baseUrl = isProd ? _baseUrl : isMock ? "/api" : _baseUrl;

      Api._instance = createClient<paths>({
        baseUrl: baseUrl,
      });
    }

    return Api._instance;
  }
}
