import {useAtomValue} from "jotai";
import {useEffect} from "react";
import {type Middleware} from "openapi-fetch";
import {Api} from "../api/baseApi.ts";
import {UserAtom} from "@/auth/user-atom.ts";

/**
 * useApiAuth
 * This hook is used to authenticate api calls with the user's token.
 */
export default function useApiAuth() {
    const user = useAtomValue(UserAtom);
    // setup auth interceptors

    useEffect(() => {
        if (!user) return;
        console.log("Setting up auth middleware");
        const authMiddleware: Middleware = {
            async onRequest({request}) {
                const token = await user.getIdToken();
                request.headers.set("Authorization", `Bearer ${token}`);
                return request;

            },
            async onResponse({response}) {
                const {body, ...resOptions} = response;
                // change status of response
                return new Response(body, {...resOptions, status: 200});
            },
        };

        Api.instance.use(authMiddleware);

        return () => {
            Api.instance.eject(authMiddleware);
        };
    }, [user]);
}
