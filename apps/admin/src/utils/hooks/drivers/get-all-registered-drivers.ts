import { useSuspenseQuery } from "@tanstack/react-query";
import { Api } from "@/utils/api/baseApi.ts";

/**
 * getdrivers
 * This hook is used to fetch the current registered driver.
 */
interface useQueryPrams {
  page?: number;
  take?: number;
}

export default function useGetAllRegisteredDrivers({
  page,
  take,
}: useQueryPrams) {
  return useSuspenseQuery({
    queryKey: ["drivers", "getAllDrivers", [page, take]],
    queryFn: async () => {
      // Fetch the truck profile from the server
      const response = await Api.instance.GET("/drivers", {
        params: {
          query: {
            page: page,
            take: take,
          },
        },
      });

      if (!response.data) {
        throw new Error("Driver profile not found");
      }

      console.log("Driver response:", response.data); // Debugging line

      // Make sure to return an array, even if the data is empty or undefined
      return response.data;
    },
  });
}
