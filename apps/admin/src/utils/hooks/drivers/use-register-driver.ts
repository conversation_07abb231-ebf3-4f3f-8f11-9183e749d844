import { useMutation } from "@tanstack/react-query";
import { Api } from "@/utils/api/baseApi.ts";

type RegisterDriver = {
  firstName: string;
  lastName: string;
  dob: string;
  drivingLicenseNumber: string;
  transportLicenseNumber: string;
  address: string;
  mobile: string;
  email: string;
  profilePicture: string;
  idCard_front: string;
  idCard_back: string;
  drivingLicense: string;
  transportLicense: string;
};

export default function useRegisterDriver() {
  return useMutation({
    mutationFn: async (driver: RegisterDriver) => {
      console.log("Driver Information: ", driver);
      const response = await Api.instance.POST(`/drivers/register-admin`, {
        body: driver, // Ensure this contains all required properties
      });

      console.log("Response:", response); // Log the entire response
      if (!response.data) {
        throw new Error("Failed to register driver");
      }
      return response.data;
    },
  });
}
