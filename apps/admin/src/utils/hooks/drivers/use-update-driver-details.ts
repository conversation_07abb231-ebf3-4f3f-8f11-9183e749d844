import { useMutation } from "@tanstack/react-query";
import { Api } from "@/utils/api/baseApi.ts";
import { components } from "@/api-schema";
import { queryClient } from "@/utils/query-client.ts";

type UpdateDriver = components["schemas"]["UpdateDriverDto"];

export default function useUpdateDriverDetails() {
  return useMutation({
    mutationFn: async (params: { driverId: string; driver: UpdateDriver }) => {
      const { driverId, driver } = params;
      const response = await Api.instance.PATCH(`/drivers/{id}`, {
        params: {
          path: { id: driverId },
        },
        body: driver,
      });

      console.log(" in Driver response:", driver); // Debugging line
      console.log(" in response response:", response); // Debugging line
      queryClient.setQueryData(["driverDetail", driverId], driver);
      if (!response.data) {
        throw new Error("Failed to update driver details");
      }

      return response.data;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: ["drivers", "getAllDrivers"],
      });
    },
  });
}
