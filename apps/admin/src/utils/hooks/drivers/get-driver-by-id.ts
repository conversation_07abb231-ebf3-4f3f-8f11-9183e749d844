import { useSuspenseQuery } from "@tanstack/react-query";
import { Api } from "@/utils/api/baseApi.ts";

/**
 * get driver
 * This hook is used to fetch the current registered driver.
 */
export default function useGetTruckDetails(driverId: string) {
  return useSuspenseQuery({
    queryKey: ["driverDetail", driverId],
    queryFn: async () => {
      // Fetch the truck profile from the server
      const response = await Api.instance.GET("/drivers/{id}", {
        params: {
          path: { id: driverId },
        },
      });
      console.log("Fetching driver : ", response); // Debugging line
      if (!response.data) throw new Error("Driver details not found");
      return response.data;
    },
  });
}
