import { useSuspenseQuery } from "@tanstack/react-query";
import { Api } from "@/utils/api/baseApi.ts";

/**
 * useGetMe
 */

export default function useGetMe() {
  return useSuspenseQuery({
    queryKey: ["Me", "getMe"], // Cache is based on the page number
    queryFn: async () => {
      const response = await Api.instance.GET(`/users/me`);

      if (!response.data) {
        throw new Error("No data found");
      }

      // Return the raw response
      return response.data;
    },
  });
}
