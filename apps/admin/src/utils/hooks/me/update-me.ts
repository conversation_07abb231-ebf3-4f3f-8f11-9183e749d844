import { useMutation } from "@tanstack/react-query";
import { Api } from "@/utils/api/baseApi.ts";
import { components } from "@/api-schema";
import { queryClient } from "@/utils/query-client.ts";

type UpdateUser = components["schemas"]["UpdateUserDto"];

export default function useUpdateUserDetails() {
  return useMutation({
    mutationFn: async (params: { userId: string; user: UpdateUser }) => {
      const { userId, user } = params;
      const response = await Api.instance.PATCH(`/users/me`, {
        body: user,
      });

      console.log("response to hook", user);

      queryClient.setQueryData(["userDetail", userId], user);
      if (!response.data) {
        throw new Error("Failed to update user details");
      }

      return response.data;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: ["user","me"]
      });
    },
  });
}
