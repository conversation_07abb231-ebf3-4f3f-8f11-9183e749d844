import { useSuspenseQuery } from "@tanstack/react-query";
import { Api } from "@/utils/api/baseApi.ts";

/**
 * getTrucks
 * This hook is used to fetch the current registered trucks.
 */
export default function useGetTruckDetails(truckId: string) {
  return useSuspenseQuery({
    queryKey: ["truckDetail", truckId],
    queryFn: async () => {
      // Fetch the truck profile from the server
      const response = await Api.instance.GET("/trucks/{id}", {
        params: {
          path: { id: truckId },
        },
      });
      if (!response.data) throw new Error("Truck details not found");
      console.log("Truck details response:", response);
      return response.data;
    },
  });
}
