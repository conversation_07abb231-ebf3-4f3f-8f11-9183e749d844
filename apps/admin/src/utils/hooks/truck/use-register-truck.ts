import { useMutation } from "@tanstack/react-query";
import { Api } from "@/utils/api/baseApi.ts";
import { components } from "@/api-schema";

type RegisterTruck = components["schemas"]["CreateTruckDto"];

export default function useRegisterTruck() {
  return useMutation({
    mutationFn: async (truck: RegisterTruck) => {
      console.log("Truck Information: ", truck);
      const response = await Api.instance.POST(`/trucks`, {
        body: truck, // Ensure this contains all required properties
      });

      console.log("Response:", response); // Log the entire response
      if (!response.data) {
        throw new Error("Failed to register truck");
      }

      return response.data;
    },
  });
}
