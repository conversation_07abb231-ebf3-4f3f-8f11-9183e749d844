import { useMutation } from "@tanstack/react-query";
import { Api } from "@/utils/api/baseApi.ts";
import { components } from "@/api-schema";
import { queryClient } from "@/utils/query-client.ts";

type UpdateTruck = components["schemas"]["UpdateTruckDto"];

export default function useUpdateTruckDetails() {
  return useMutation({
    mutationFn: async (params: { truckId: string; truck: UpdateTruck }) => {
      const { truckId, truck } = params;
      const response = await Api.instance.PATCH(`/trucks/{id}`, {
        params: {
          path: { id: truckId },
        },
        body: truck,
      });

      queryClient.setQueryData(["truckDetail", truckId], truck);
      if (!response.data) {
        throw new Error("Failed to update truck details");
      }

      return response.data;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: ["trucks", "getAllTrucks"],
      });
    },
  });
}
