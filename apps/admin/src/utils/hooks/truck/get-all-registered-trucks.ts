import { useSuspenseQuery } from "@tanstack/react-query";
import { Api } from "@/utils/api/baseApi.ts";

/**
 * getTrucks
 * This hook is used to fetch the current registered trucks.
 */

interface useQueryPrams {
  page?: number;
  take?: number;
}

export default function useGetAllRegisteredTrucks({
  page = 1,
  take = 10,
}: useQueryPrams) {
  return useSuspenseQuery({
    queryKey: ["trucks", "getAllTrucks", [page, take]], // Cache is based on the page number
    queryFn: async () => {
      // Fetch the truck profile from the server
      console.log("Fetching trucks... Page : ", page, take); // Debugging line
      const response = await Api.instance.GET("/trucks", {
        prams: {
          query: {
            page: page,
            take: take,
          },
        },
      });

      if (!response.data) {
        throw new Error("Truck profile not found");
      }

      console.log("Truck response:", response); // Debugging line

      // Make sure to return an array, even if the data is empty or undefined
      return response.data;
    },
  });
}
