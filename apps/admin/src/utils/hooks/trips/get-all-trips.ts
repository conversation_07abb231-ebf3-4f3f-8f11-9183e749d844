import { useSuspenseQuery } from "@tanstack/react-query";
import { Api } from "@/utils/api/baseApi.ts";

/**
 * useGetAllTrips
 * This hook fetches paginated trips with pagination metadata.
 */

interface useQueryPrams {
  page?: number;
  take?: number;
}

export default function useGetAllTrips({ page = 1, take = 10 }: useQueryPrams) {
  return useSuspenseQuery({
    queryKey: ["trips", "getAllTrips", [page, take]], // Cache is based on the page number
    queryFn: async () => {
      const response = await Api.instance.GET(`/trips`, {
        params: {
          query: {
            page: page,
            take: take,
          },
        },
      });
      if (!response.data) {
        throw new Error("No data found");
      }
      console.log("Trips response:", response.data);

      // Return the raw response
      return response.data;
    },
  });
}
