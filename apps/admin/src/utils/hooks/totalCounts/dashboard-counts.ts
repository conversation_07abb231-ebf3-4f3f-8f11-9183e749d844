import { useSuspenseQuery } from "@tanstack/react-query";
import { Api } from "@/utils/api/baseApi.ts";
import { components } from "@/api-schema";

/**
 * useGetAllRegisteredUsers
 * This hook fetches paginated users with pagination metadata.
 */

type DashboardStatsDto = components["schemas"]["DashboardStatsDto"];

export default function useDashboard() {
  return useSuspenseQuery<DashboardStatsDto>({
    queryKey: ["total", "getAllTotal"], // Cache is based on the page number
    queryFn: async (): Promise<DashboardStatsDto> => {
      const response = await Api.instance.GET(`/dashboard`,
        {
          params: {
            query: {
              startDate: "2021-01-01",
              endDate: "2025-12-31",
            }
          },
          }
        );

      if (!response.data) {
        throw new Error("No data found");
      }

      // Return the raw response
      return response.data;
    },
  });
}
