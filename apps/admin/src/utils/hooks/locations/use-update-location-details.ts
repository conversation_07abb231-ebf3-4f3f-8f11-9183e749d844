import { useMutation } from "@tanstack/react-query";
import { Api } from "@/utils/api/baseApi.ts";
import { components } from "@/api-schema";
import { queryClient } from "@/utils/query-client.ts";

type UpdateLocation = components["schemas"]["UpdateLocationDto"];

export default function useUpdateLocationDetails() {
  return useMutation({
    mutationFn: async (params: {
      locationId: string;
      location: UpdateLocation;
    }) => {
      const { locationId, location } = params;
      const response = await Api.instance.PATCH(`/locations/{id}`, {
        params: {
          path: { id: locationId },
        },
        body: location,
      });

      queryClient.setQueryData(["location details", locationId], location);
      if (!response.data) {
        throw new Error("Failed to update location details");
      }

      return response.data;
    },
    onSuccess: async () => {
      await queryClient.invalidateQueries({
        queryKey: ["locations", "getAllLocations"],
      });
    },
  });
}
