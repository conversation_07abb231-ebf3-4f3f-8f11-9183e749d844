import { useMutation } from "@tanstack/react-query";
import { Api } from "@/utils/api/baseApi.ts";
import { components } from "@/api-schema";

type RegisterLocation = components["schemas"]["CreateLocationDto"];

export default function useRegisterLocation() {
  return useMutation({
    mutationFn: async (location: RegisterLocation) => {
      console.log("location Information: ", location);
      const response = await Api.instance.POST(`/locations`, {
        body: location, // Ensure this contains all required properties
      });

      console.log("Response:", response); // Log the entire response
      if (!response.data) {
        throw new Error("Failed to register location");
      }

      return response.data;
    },
  });
}
