import { useSuspenseQuery } from "@tanstack/react-query";
import { Api } from "@/utils/api/baseApi.ts";

/**
 * getLocations
 * This hook is used to fetch the current registered locations.
 */

interface useQueryPrams {
  page?: number;
  take?: number;
}

export default function useGetAllRegisteredLocations({
  page = 1,
  take = 10,
}: useQueryPrams) {
  return useSuspenseQuery({
    queryKey: ["locations", "getAllLocations", [page, take]], // Cache is based on the page number
    queryFn: async () => {
      // Fetch the truck profile from the server
      console.log("Fetching locations... Page : ", page, take); // Debugging line
      const response = await Api.instance.GET("/locations", {
        prams: {
          query: {
            page: page,
            take: take,
          },
        },
      });

      if (!response.data) {
        throw new Error("Locations profile not found");
      }

      console.log("Locations response:", response); // Debugging line

      // Make sure to return an array, even if the data is empty or undefined
      return response.data;
    },
  });
}
