import { useSuspenseQuery } from "@tanstack/react-query";
import { Api } from "@/utils/api/baseApi.ts";

/**
 * getLocationDetails
 * This hook is used to fetch the current registered trucks.
 */
export default function useGetLocationDetails(location: string) {
  return useSuspenseQuery({
    queryKey: ["locationDetail", location],
    queryFn: async () => {
      // Fetch the truck profile from the server
      const response = await Api.instance.GET("/locations/{id}", {
        params: {
          path: { id: location },
        },
      });
      if (!response.data) throw new Error("location details not found");
      console.log("location details response:", response);
      return response.data;
    },
  });
}
