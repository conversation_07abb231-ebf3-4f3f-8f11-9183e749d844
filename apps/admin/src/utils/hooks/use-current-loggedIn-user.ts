import { useSuspenseQuery } from "@tanstack/react-query";
import { Api } from "@/utils/api/baseApi.ts";

/**
 * useUserProfile
 * This hook is used to fetch the current user's profile.
 */
export default function useUserProfile() {
  return useSuspenseQuery({
    queryKey: ["user", "me"],
    queryFn: async () => {
      //Fetch the user profile from the server
      const response = await Api.instance.GET("/users/me");
      if (!response.data) throw new Error("User profile not found");
      return response.data;
    },
  });
}
