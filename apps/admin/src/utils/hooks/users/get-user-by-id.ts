import {useSuspenseQuery} from "@tanstack/react-query";
import {Api} from "@/utils/api/baseApi.ts";

/**
 * getUser
 * This hook is used to fetch the current registered user.
 */
export default function useGetTruckDetails(userId: string) {
  return useSuspenseQuery({
    queryKey: ["userDetail", userId],
    queryFn: async () => {
      // Fetch the truck profile from the server
      const response = await Api.instance.GET("/users/{id}", {
        params: {
          path: { id: userId },
        },
      });
      if (!response.data) throw new Error("User details not found");
      return response.data;
    },
  });
}
