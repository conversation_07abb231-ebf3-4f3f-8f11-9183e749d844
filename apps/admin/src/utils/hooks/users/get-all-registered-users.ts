import { useSuspenseQuery } from "@tanstack/react-query";
import { Api } from "@/utils/api/baseApi.ts";

/**
 * useGetAllRegisteredUsers
 * This hook fetches paginated users with pagination metadata.
 */

interface useQueryPrams {
  page?: number;
  take?: number;
  role?: "CUSTOMER" | "DRIVER" | "ADMIN" | "MANAGER" | "STAFF" | "POLICE";
}

export default function useGetAllRegisteredUsers({
  page,
  take,
  role,
}: useQueryPrams) {
  return useSuspenseQuery({
    queryKey: ["users", "getAllUsers", [page, take, role]], // Cache is based on the page number
    queryFn: async () => {
      const response = await Api.instance.GET(`/users`, {
        params: {
          query: {
            page: page,
            take: take,
            role: role,
          },
        },
      });

      if (!response.data) {
        throw new Error("No users found");
      }

      // Return the raw response
      return response.data;
    },
  });
}
