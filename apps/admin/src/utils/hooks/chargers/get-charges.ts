import { Api } from "@/utils/api/baseApi.ts";
import { useSuspenseQuery } from "@tanstack/react-query";

/**
 * getSpace
 * This hook is used to fetch the current space.
 */

export default function useGetDestinationCharges(type: string) {
  return useSuspenseQuery({
    queryKey: ["charges", "getCharges", type],
    queryFn: async () => {
      // Fetch the truck profile from the server
      const response = await Api.instance.GET("/settings/type/{type}", {
        params: {
          path: { type: type },
        },
      });

      if (!response.data) {
        throw new Error("Space not found");
      }
      return response.data;
    },
  });
}
