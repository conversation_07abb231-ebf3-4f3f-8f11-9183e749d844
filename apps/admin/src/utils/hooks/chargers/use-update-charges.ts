import { useMutation } from "@tanstack/react-query";
import { Api } from "@/utils/api/baseApi.ts";
import { queryClient } from "@/utils/query-client.ts";
import { components } from "@/api-schema";

/**
 * updateCharges
 * This hook is used to update the destination charges.
 */

type UpdateCharges = components["schemas"]["TripSettingsDto"];

export default function useUpdateCharges(type: string) {
  return useMutation({
    mutationFn: async (data: UpdateCharges) => {
      // Make the API request to update the charges
      const response = await Api.instance.PATCH("/settings/{type}", {
        params: {
          path: { type: type },
        },
        body: data,
      });

      if (!response.data) {
        throw new Error("Failed to update destination charges");
      }

      return response.data;
    },
    onSuccess: async () => {
      // Invalidate the query related to destination charges
      await queryClient.invalidateQueries({
        queryKey: ["charges", "getCharges", type],
      });
    },
  });
}
