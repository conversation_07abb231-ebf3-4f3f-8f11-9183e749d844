import { auth } from "@/auth/auth.ts";
import { useSet<PERSON>tom } from "jotai";
import { User<PERSON>tom } from "@/auth/user-atom.ts";
import { PropsWithChildren, useEffect, useState } from "react";
import { User } from "firebase/auth";
import useApiAuth from "@/utils/api/use-api-auth.ts";

interface AuthProviderProps extends PropsWithChildren {}

export function AuthProvider({ children }: AuthProviderProps) {
  const setUser = useSetAtom(UserAtom);
  const [load, setLoad] = useState(false);
  useApiAuth();
  useEffect(() => {
    function onUserUpdate(user: User | null) {
      if (!user) {
        setUser(undefined);
        setLoad(true);
        return;
      }
      setUser(user);
      setLoad(true);
    }

    return auth.onAuthStateChanged(onUserUpdate);
  }, [setUser]);

  if (!load) return null;
  return children;
}
