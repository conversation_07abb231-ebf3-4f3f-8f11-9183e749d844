import { User<PERSON><PERSON> } from "@/auth/user-atom.ts";
import { useAtomValue } from "jotai";
import {
  signInWithEmailAndPassword,
  signInWithPopup,
  signOut,
} from "firebase/auth";
import { auth, provider } from "@/auth/auth.ts";

export function useAuth() {
  const user = useAtomValue(UserAtom);

  async function loginWithEmailAndPassword(email: string, password: string) {
    return await signInWithEmailAndPassword(auth, email, password);
  }

  async function loginWithGoogle() {
    return await signInWithPopup(auth, provider);
  }

  async function logout() {
    await signOut(auth);
    window.location.reload();
  }

  return {
    user,
    loginWithEmailAndPassword,
    loginWithGoogle,
    logout,
  };
}
