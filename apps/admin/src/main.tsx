import { createRoot } from "react-dom/client";
import "./index.css";
import { ThemeProvider } from "@/components/ThemeProvider.tsx";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { QueryClientProvider } from "@tanstack/react-query";
import { createRouter, RouterProvider } from "@tanstack/react-router";
import { queryClient } from "@/utils/query-client.ts";
import { routeTree } from "@/routeTree.gen.ts";
import { AuthProvider } from "@/auth/auth-provider.ts";
import { auth } from "@/auth/auth.ts";
import "react-toastify/dist/ReactToastify.css";
import { ToastContainer } from "react-toastify";
import { HeroUIProvider } from "@heroui/react";
import { StrictMode } from "react";

const router = createRouter({
  routeTree,
  context: {
    queryClient: queryClient,
    user: auth.currentUser,
  },
});

declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}

const root = createRoot(document.getElementById("root")!);
root.render(
  <StrictMode>
    <HeroUIProvider>
      <ToastContainer />
      <QueryClientProvider client={queryClient}>
        <ThemeProvider defaultTheme="light">
          <AuthProvider>
            <RouterProvider router={router} />
          </AuthProvider>
        </ThemeProvider>
        <ReactQueryDevtools buttonPosition="top-left" />
      </QueryClientProvider>
    </HeroUIProvider>
  </StrictMode>
);
