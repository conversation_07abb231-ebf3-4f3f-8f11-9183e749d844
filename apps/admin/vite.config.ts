import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import { TanStackRouterVite } from "@tanstack/router-plugin/vite";
import tsconfigPaths from "vite-tsconfig-paths";
import checker from "vite-plugin-checker";

const disableChecker = process.env.DISABLE_CHECKER === "true";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    disableChecker
      ? undefined
      : checker({
          typescript: true,
          eslint: {
            // useFlatConfig: true,
            lintCommand: 'eslint "./src/**/*.{ts,tsx}"',
          },
          enableBuild: false,
        }),
    tsconfigPaths(),
    TanStackRouterVite(),
    react(),
  ],
  optimizeDeps: {
    exclude: [
      "firebase",
      "firebase/app",
      "firebase/auth",
      "firebase/firestore",
      "firebase/analytics",
      "firebase/storage",
    ],
  },
});
