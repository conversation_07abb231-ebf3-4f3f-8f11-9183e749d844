{"name": "admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "bunx --bun vite", "build": "tsc -b && DISABLE_CHECKER=true bunx --bun vite build", "serve": "vite build --mode production && vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "sync:types": "openapi-typescript https://tg.sv.devntion.com/docs-json -o ./src/api-schema.d.ts", "sync:types_local": "openapi-typescript http://localhost:5001/docs-json -o ./src/api-schema.d.ts", "test:ts": "tsc --noEmit"}, "dependencies": {"@heroui/react": "^2.7.6", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@tanstack/react-form": "^0.24.3", "@tanstack/react-query": "^5.77.0", "@tanstack/react-query-devtools": "^5.77.0", "@tanstack/react-router": "^1.114.29", "@tanstack/react-router-devtools": "^1.114.31", "@tanstack/react-table": "^8.21.2", "@tanstack/zod-form-adapter": "^0.26.4", "@vitejs/plugin-react-swc": "^3.8.1", "@x/api": "workspace:*", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^11.6.0", "framer-motion": "^11.18.2", "jotai": "^2.12.2", "lucide-react": "^0.477.0", "openapi-fetch": "^0.13.5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.55.0", "react-phone-input-2": "^2.15.1", "react-phone-number-input": "^3.4.12", "react-toastify": "^10.0.6", "recharts": "^2.15.1", "tailwind-merge": "^3.1.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@tanstack/eslint-plugin-query": "^5.68.0", "@tanstack/router-plugin": "^1.114.31", "@types/node": "^22.13.15", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@types/recharts": "^1.8.29", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "autoprefixer": "^10.4.21", "eslint": "^8.57.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "openapi-typescript": "^7.6.1", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.17", "tailwindcss-animated": "^1.1.2", "typescript": "^5.0.0", "vite": "^6.2.4", "vite-plugin-checker": "^0.9.1", "vite-tsconfig-paths": "^5.1.4"}}