# syntax=docker/dockerfile:1.7-labs

FROM oven/bun:1.2 AS base

WORKDIR /app


FROM base AS deps

# copy all package.json files and bun.lock file
COPY --exclude=* --exclude=!**/package.json . .
COPY bun.lock .

# install dependencies for admin app
RUN bun install --filter admin


FROM base AS builder

# copy all node_modules from deps stage
COPY --from=deps --exclude=* --exclude=!**/node_modules /app .

# copy all files
COPY . .

WORKDIR /app/apps/admin

ENV NODE_ENV=production
ENV VITE_API_URL=https://tg.sv.devntion.com

# build admin app
RUN bun run build


FROM nginx:alpine

COPY --from=builder /app/apps/admin/dist /usr/share/nginx/html
COPY --from=builder /app/apps/admin/nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
